<?php
/**
 * Lightweight scraper that won't crash the server
 */

require_once 'config/config.php';
require_once 'includes/RedditScraper.php';

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_GET['action']) {
            case 'run_light_scraper':
                $scraper = new RedditScraper();
                
                // Scrape just one subreddit with limited posts
                $subreddit = $_GET['subreddit'] ?? 'dubai';
                $limit = min((int)($_GET['limit'] ?? 10), 20); // Max 20 posts
                
                $results = $scraper->scrapeSubreddit($subreddit, $limit);
                
                echo json_encode([
                    'success' => true,
                    'results' => $results,
                    'message' => "Scraped r/{$subreddit}: {$results['posts']} posts, {$results['comments']} comments, {$results['leads']} leads"
                ]);
                exit;
                
            case 'get_stats':
                $db = Database::getInstance();
                $pdo = $db->getPDO();
                
                $stats = [];
                $stats['total_posts'] = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
                $stats['total_comments'] = $pdo->query("SELECT COUNT(*) FROM comments")->fetchColumn();
                $stats['total_leads'] = $pdo->query("SELECT COUNT(*) FROM leads")->fetchColumn();
                
                echo json_encode($stats);
                exit;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Light Scraper - Dubai Real Estate</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <h2><i class="fas fa-robot"></i> Light Reddit Scraper</h2>
                <p class="text-muted">Lightweight scraper that won't crash the server</p>
                
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Subreddit:</label>
                                <select class="form-select" id="subreddit-select">
                                    <option value="dubai">r/dubai</option>
                                    <option value="dubairealestate">r/dubairealestate</option>
                                    <option value="UAE">r/UAE</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Posts to scrape:</label>
                                <select class="form-select" id="limit-select">
                                    <option value="5">5 posts</option>
                                    <option value="10" selected>10 posts</option>
                                    <option value="15">15 posts</option>
                                    <option value="20">20 posts</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="runLightScraper()">
                                <i class="fas fa-play"></i> Run Light Scraper
                            </button>
                            <button class="btn btn-info" onclick="refreshStats()">
                                <i class="fas fa-refresh"></i> Refresh Stats
                            </button>
                        </div>
                        
                        <div id="scraper-status" class="mt-3"></div>
                    </div>
                </div>
                
                <!-- Stats -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Current Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div id="stats-display">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i> Loading...
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="mt-4">
                    <a href="leads.php" class="btn btn-success">
                        <i class="fas fa-users"></i> View Leads
                    </a>
                    <a href="index.php" class="btn btn-outline-primary">
                        <i class="fas fa-dashboard"></i> Dashboard
                    </a>
                    <a href="export.php" class="btn btn-outline-success">
                        <i class="fas fa-download"></i> Export
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load stats on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
        });
        
        function runLightScraper() {
            const subreddit = document.getElementById('subreddit-select').value;
            const limit = document.getElementById('limit-select').value;
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scraping...';
            
            document.getElementById('scraper-status').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i> Scraping r/${subreddit} (${limit} posts)...
                </div>
            `;
            
            fetch(`?action=run_light_scraper&subreddit=${subreddit}&limit=${limit}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-success">
                                <h6>✅ Scraping Completed!</h6>
                                <p>${data.message}</p>
                            </div>
                        `;
                        refreshStats();
                    } else {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-danger">
                                <h6>❌ Scraping Failed</h6>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('scraper-status').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ Error</h6>
                            <p>Network error: ${error.message}</p>
                        </div>
                    `;
                })
                .finally(() => {
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
        }
        
        function refreshStats() {
            fetch('?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('stats-display').innerHTML = `
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="h3 text-primary">${data.total_leads}</div>
                                <small>Total Leads</small>
                            </div>
                            <div class="col-4">
                                <div class="h3 text-success">${data.total_posts}</div>
                                <small>Posts</small>
                            </div>
                            <div class="col-4">
                                <div class="h3 text-info">${data.total_comments}</div>
                                <small>Comments</small>
                            </div>
                        </div>
                    `;
                })
                .catch(error => {
                    document.getElementById('stats-display').innerHTML = `
                        <div class="text-danger">Error loading stats</div>
                    `;
                });
        }
    </script>
</body>
</html>
