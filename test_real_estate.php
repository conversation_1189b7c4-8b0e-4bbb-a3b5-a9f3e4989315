<?php
/**
 * Test scraping real estate subreddit for leads
 */

require_once 'config/config.php';
require_once 'includes/RedditScraper.php';

try {
    echo "Testing real estate lead detection...\n";
    
    $scraper = new RedditScraper();
    
    // Test scraping dubairealestate subreddit
    echo "Scraping r/dubairealestate (limited to 10 posts)...\n";
    $results = $scraper->scrapeSubreddit('dubairealestate', 10);
    
    echo "✅ Scraping completed!\n";
    echo "Posts processed: " . $results['posts'] . "\n";
    echo "Comments processed: " . $results['comments'] . "\n";
    echo "Leads found: " . $results['leads'] . "\n";
    
    // Get updated stats
    $stats = $scraper->getStats();
    echo "\nTotal leads in database: " . $stats['total_leads'] . "\n";
    
    if ($stats['total_leads'] > 0) {
        echo "\n🎉 Great! Found leads in the database.\n";
        echo "Check the web dashboard at http://localhost:8000 to view them!\n";
    } else {
        echo "\nNo leads found yet. This could be because:\n";
        echo "- Recent posts don't contain contact information\n";
        echo "- Posts don't meet the intent scoring threshold\n";
        echo "- The subreddit has low activity\n";
        echo "\nTry running the full scraper on all subreddits for better results.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ Application is fully functional!\n";
echo "🌐 Web Dashboard: http://localhost:8000\n";
echo "🔧 Scraper Control: http://localhost:8000/scraper.php\n";
echo "📊 Export Leads: http://localhost:8000/export.php\n";
?>
