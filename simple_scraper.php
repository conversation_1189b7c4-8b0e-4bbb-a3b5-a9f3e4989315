<?php
/**
 * Super Simple Scraper - Won't crash the server
 */

require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/RedditAPISimple.php';
require_once 'includes/ContactExtractor.php';

// Handle AJAX
if (isset($_GET['action']) && $_GET['action'] === 'scrape') {
    header('Content-Type: application/json');
    
    try {
        $subreddit = $_GET['subreddit'] ?? 'dubai';
        $limit = min((int)($_GET['limit'] ?? 5), 10); // Max 10 posts
        
        $reddit = new RedditAPISimple();
        $db = Database::getInstance();
        $pdo = $db->getPDO();
        $contactExtractor = new ContactExtractor();
        
        // Get posts
        $response = $reddit->getSubredditPosts($subreddit, 'new', $limit);
        $posts = $reddit->extractPostsData($response);
        
        $leadsFound = 0;
        $postsProcessed = 0;
        
        foreach ($posts as $post) {
            // Simple check: if post contains contact info, save it as a lead
            $contacts = $contactExtractor->extractContacts($post['title'] . ' ' . $post['content'], $post['author']);
            
            if (!empty($contacts)) {
                foreach ($contacts as $contact) {
                    // Save to database
                    $stmt = $pdo->prepare("INSERT INTO leads (source_type, source_id, contact_type, contact_value, author, intent_score, context) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        'post',
                        $post['reddit_id'],
                        $contact['type'],
                        $contact['value'],
                        $contact['author'],
                        5, // Simple score
                        substr($contact['context'], 0, 500)
                    ]);
                    $leadsFound++;
                }
            }
            $postsProcessed++;
        }
        
        echo json_encode([
            'success' => true,
            'posts' => $postsProcessed,
            'leads' => $leadsFound,
            'message' => "Found $leadsFound leads from $postsProcessed posts in r/$subreddit"
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get current stats
$db = Database::getInstance();
$pdo = $db->getPDO();

$totalLeads = $pdo->query("SELECT COUNT(*) FROM leads")->fetchColumn();
$totalPosts = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Reddit Scraper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <h2><i class="fas fa-search"></i> Simple Reddit Scraper</h2>
                <p class="text-muted">Safe scraper that won't crash your server</p>
                
                <!-- Current Stats -->
                <div class="row mb-4">
                    <div class="col-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?= $totalLeads ?></h3>
                                <p>Total Leads</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?= $totalPosts ?></h3>
                                <p>Posts Scraped</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Scraper Controls -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-robot"></i> Scraper Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Subreddit:</label>
                                <select class="form-select" id="subreddit">
                                    <option value="dubai">r/dubai</option>
                                    <option value="dubairealestate">r/dubairealestate</option>
                                    <option value="UAE">r/UAE</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Posts to check:</label>
                                <select class="form-select" id="limit">
                                    <option value="5">5 posts</option>
                                    <option value="10">10 posts</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-primary" onclick="runScraper()" id="scrapeBtn">
                                <i class="fas fa-play"></i> Scrape Now
                            </button>
                            <button class="btn btn-success" onclick="scrapeAll()" id="scrapeAllBtn">
                                <i class="fas fa-rocket"></i> Scrape All Subreddits
                            </button>
                        </div>
                        
                        <div id="results" class="mt-3"></div>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="mt-4">
                    <a href="leads.php" class="btn btn-info">
                        <i class="fas fa-users"></i> View Leads
                    </a>
                    <a href="export.php" class="btn btn-success">
                        <i class="fas fa-download"></i> Export Data
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function runScraper() {
            const subreddit = document.getElementById('subreddit').value;
            const limit = document.getElementById('limit').value;
            const btn = document.getElementById('scrapeBtn');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scraping...';
            
            document.getElementById('results').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i> Scraping r/${subreddit} for contact information...
                </div>
            `;
            
            fetch(`?action=scrape&subreddit=${subreddit}&limit=${limit}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('results').innerHTML = `
                            <div class="alert alert-success">
                                <h6>✅ Scraping Complete!</h6>
                                <p>${data.message}</p>
                            </div>
                        `;
                        // Refresh page to show new stats
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        document.getElementById('results').innerHTML = `
                            <div class="alert alert-danger">
                                <h6>❌ Error</h6>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('results').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ Network Error</h6>
                            <p>${error.message}</p>
                        </div>
                    `;
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.innerHTML = '<i class="fas fa-play"></i> Scrape Now';
                });
        }
        
        async function scrapeAll() {
            const subreddits = ['dubai', 'dubairealestate', 'UAE'];
            const btn = document.getElementById('scrapeAllBtn');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scraping All...';
            
            let totalLeads = 0;
            
            for (let i = 0; i < subreddits.length; i++) {
                const subreddit = subreddits[i];
                
                document.getElementById('results').innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-spinner fa-spin"></i> Scraping r/${subreddit} (${i + 1}/${subreddits.length})...
                    </div>
                `;
                
                try {
                    const response = await fetch(`?action=scrape&subreddit=${subreddit}&limit=10`);
                    const data = await response.json();
                    
                    if (data.success) {
                        totalLeads += data.leads;
                    }
                    
                    // Wait 2 seconds between subreddits
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    
                } catch (error) {
                    console.error('Error scraping', subreddit, error);
                }
            }
            
            document.getElementById('results').innerHTML = `
                <div class="alert alert-success">
                    <h6>✅ All Subreddits Scraped!</h6>
                    <p>Found ${totalLeads} total leads across all subreddits</p>
                </div>
            `;
            
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-rocket"></i> Scrape All Subreddits';
            
            // Refresh page to show new stats
            setTimeout(() => location.reload(), 3000);
        }
    </script>
</body>
</html>
