<?php
/**
 * Automated Reddit Scraper for Cron Jobs
 * 
 * This script is designed to be run via cron for automated scraping.
 * Add to crontab: 0 * * * * /usr/bin/php /path/to/your/project/cron_scraper.php
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/RedditScraper.php';

// Ensure script runs only from command line or cron
if (isset($_SERVER['HTTP_HOST'])) {
    die('This script can only be run from command line or cron.');
}

// Set up logging
$logFile = __DIR__ . '/logs/cron_scraper.log';
$logDir = dirname($logFile);

if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function logMessage($message, $level = 'INFO') {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] [$level] $message" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    echo $logEntry; // Also output to console
}

function shouldRunScraper() {
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Check when we last ran a successful scrape
    $stmt = $pdo->query("
        SELECT MAX(created_at) as last_run 
        FROM scraping_logs 
        WHERE status = 'success' AND action = 'scrape_subreddit'
    ");
    $result = $stmt->fetch();
    
    if (!$result['last_run']) {
        return true; // Never run before
    }
    
    $lastRun = new DateTime($result['last_run']);
    $now = new DateTime();
    $interval = $now->diff($lastRun);
    
    // Run if it's been more than SCRAPING_INTERVAL_MINUTES since last successful run
    $minutesSinceLastRun = ($interval->h * 60) + $interval->i;
    
    return $minutesSinceLastRun >= SCRAPING_INTERVAL_MINUTES;
}

function getScrapingPriority() {
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Get subreddits ordered by when they were last scraped (oldest first)
    $stmt = $pdo->query("
        SELECT name, last_scraped, total_posts_scraped
        FROM subreddits 
        ORDER BY 
            CASE WHEN last_scraped IS NULL THEN 0 ELSE 1 END,
            last_scraped ASC
    ");
    
    return $stmt->fetchAll();
}

function runIncrementalScraper() {
    logMessage("Starting incremental scraper...");
    
    try {
        $scraper = new RedditScraper();
        $subreddits = getScrapingPriority();
        
        $totalResults = [
            'total_posts' => 0,
            'total_comments' => 0,
            'total_leads' => 0,
            'subreddits_scraped' => 0
        ];
        
        // Scrape 2-3 subreddits per run to avoid rate limiting
        $maxSubredditsPerRun = 3;
        $scraped = 0;
        
        foreach ($subreddits as $subreddit) {
            if ($scraped >= $maxSubredditsPerRun) {
                break;
            }
            
            try {
                logMessage("Scraping r/{$subreddit['name']}...");
                
                // Limit posts per subreddit for incremental runs
                $results = $scraper->scrapeSubreddit($subreddit['name'], 50);
                
                $totalResults['total_posts'] += $results['posts'];
                $totalResults['total_comments'] += $results['comments'];
                $totalResults['total_leads'] += $results['leads'];
                $totalResults['subreddits_scraped']++;
                
                logMessage("Completed r/{$subreddit['name']}: {$results['posts']} posts, {$results['comments']} comments, {$results['leads']} leads");
                
                $scraped++;
                
                // Rate limiting between subreddits
                sleep(2);
                
            } catch (Exception $e) {
                logMessage("Error scraping r/{$subreddit['name']}: " . $e->getMessage(), 'ERROR');
            }
        }
        
        logMessage("Incremental scraper completed. Total: {$totalResults['total_posts']} posts, {$totalResults['total_comments']} comments, {$totalResults['total_leads']} leads from {$totalResults['subreddits_scraped']} subreddits");
        
        return $totalResults;
        
    } catch (Exception $e) {
        logMessage("Fatal error in incremental scraper: " . $e->getMessage(), 'ERROR');
        throw $e;
    }
}

function runFullScraper() {
    logMessage("Starting full scraper...");
    
    try {
        $scraper = new RedditScraper();
        $results = $scraper->scrapeAll();
        
        logMessage("Full scraper completed. Total: {$results['total_posts']} posts, {$results['total_comments']} comments, {$results['total_leads']} leads");
        
        return $results;
        
    } catch (Exception $e) {
        logMessage("Fatal error in full scraper: " . $e->getMessage(), 'ERROR');
        throw $e;
    }
}

function cleanupOldLogs() {
    global $logFile;
    
    // Keep only last 30 days of logs
    if (file_exists($logFile)) {
        $lines = file($logFile);
        $cutoffDate = date('Y-m-d', strtotime('-30 days'));
        
        $filteredLines = array_filter($lines, function($line) use ($cutoffDate) {
            if (preg_match('/^\[(\d{4}-\d{2}-\d{2})/', $line, $matches)) {
                return $matches[1] >= $cutoffDate;
            }
            return true;
        });
        
        if (count($filteredLines) < count($lines)) {
            file_put_contents($logFile, implode('', $filteredLines));
            logMessage("Cleaned up old log entries");
        }
    }
}

function sendNotification($results, $type = 'success') {
    // This function can be extended to send email notifications
    // For now, just log the notification
    
    if ($type === 'success' && $results['total_leads'] > 0) {
        logMessage("NOTIFICATION: Found {$results['total_leads']} new leads!", 'NOTIFICATION');
    } elseif ($type === 'error') {
        logMessage("NOTIFICATION: Scraper encountered errors", 'NOTIFICATION');
    }
}

// Main execution
try {
    logMessage("=== Cron Scraper Started ===");
    
    // Check if we should run
    if (!shouldRunScraper()) {
        logMessage("Skipping scraper run - not enough time has passed since last successful run");
        exit(0);
    }
    
    // Check Reddit API credentials
    if (empty(REDDIT_CLIENT_ID) || empty(REDDIT_CLIENT_SECRET)) {
        logMessage("Reddit API credentials not configured. Please update config.php", 'ERROR');
        exit(1);
    }
    
    // Determine run type based on time of day
    $hour = (int)date('H');
    $isFullScrapeTime = in_array($hour, [2, 8, 14, 20]); // Full scrapes at 2am, 8am, 2pm, 8pm
    
    if ($isFullScrapeTime) {
        $results = runFullScraper();
    } else {
        $results = runIncrementalScraper();
    }
    
    // Send notification if we found leads
    sendNotification($results, 'success');
    
    // Cleanup old logs
    cleanupOldLogs();
    
    logMessage("=== Cron Scraper Completed Successfully ===");
    
} catch (Exception $e) {
    logMessage("=== Cron Scraper Failed ===", 'ERROR');
    logMessage("Error: " . $e->getMessage(), 'ERROR');
    logMessage("Stack trace: " . $e->getTraceAsString(), 'ERROR');
    
    sendNotification([], 'error');
    
    exit(1);
}

exit(0);
?>
