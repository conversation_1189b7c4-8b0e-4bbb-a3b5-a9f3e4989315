<?php
/**
 * Export functionality for Reddit Dubai Real Estate Scraper
 */

require_once 'config/config.php';
require_once 'includes/Database.php';

$db = Database::getInstance();
$pdo = $db->getPDO();

// Get export parameters
$format = $_GET['format'] ?? 'csv';
$status = $_GET['status'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$contactType = $_GET['contact_type'] ?? '';

// Build WHERE clause
$where = [];
$params = [];

if ($status) {
    $where[] = "l.status = ?";
    $params[] = $status;
}

if ($dateFrom) {
    $where[] = "DATE(l.created_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $where[] = "DATE(l.created_at) <= ?";
    $params[] = $dateTo;
}

if ($contactType) {
    $where[] = "l.contact_type = ?";
    $params[] = $contactType;
}

$whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

// Get leads data
$sql = "SELECT l.*, 
               CASE 
                   WHEN l.source_type = 'post' THEN p.title 
                   ELSE SUBSTR(c.content, 1, 100) 
               END as source_content,
               CASE 
                   WHEN l.source_type = 'post' THEN s.name 
                   ELSE (SELECT s2.name FROM posts p2 JOIN subreddits s2 ON p2.subreddit_id = s2.id JOIN comments c2 ON c2.post_id = p2.id WHERE c2.reddit_id = l.source_id LIMIT 1)
               END as subreddit,
               CASE 
                   WHEN l.source_type = 'post' THEN p.url 
                   ELSE CONCAT('https://reddit.com/r/', (SELECT s2.name FROM posts p2 JOIN subreddits s2 ON p2.subreddit_id = s2.id JOIN comments c2 ON c2.post_id = p2.id WHERE c2.reddit_id = l.source_id LIMIT 1), '/comments/', (SELECT p2.reddit_id FROM posts p2 JOIN comments c2 ON c2.post_id = p2.id WHERE c2.reddit_id = l.source_id LIMIT 1))
               END as source_url
        FROM leads l
        LEFT JOIN posts p ON l.source_type = 'post' AND l.source_id = p.id
        LEFT JOIN comments c ON l.source_type = 'comment' AND l.source_id = c.reddit_id
        LEFT JOIN subreddits s ON p.subreddit_id = s.id
        $whereClause
        ORDER BY l.created_at DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$leads = $stmt->fetchAll();

// Handle different export formats
switch ($format) {
    case 'csv':
        exportCSV($leads);
        break;
    case 'json':
        exportJSON($leads);
        break;
    case 'excel':
        exportExcel($leads);
        break;
    default:
        // Show export form
        showExportForm();
}

function exportCSV($leads) {
    $filename = 'dubai_real_estate_leads_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $output = fopen('php://output', 'w');
    
    // CSV headers
    fputcsv($output, [
        'ID',
        'Contact Type',
        'Contact Value',
        'Author',
        'Subreddit',
        'Intent Score',
        'Status',
        'Source Type',
        'Source Content',
        'Source URL',
        'Context',
        'Notes',
        'Created At',
        'Updated At'
    ]);
    
    // CSV data
    foreach ($leads as $lead) {
        fputcsv($output, [
            $lead['id'],
            $lead['contact_type'],
            $lead['contact_value'],
            $lead['author'],
            $lead['subreddit'],
            $lead['intent_score'],
            $lead['status'],
            $lead['source_type'],
            $lead['source_content'],
            $lead['source_url'],
            $lead['context'],
            $lead['notes'],
            $lead['created_at'],
            $lead['updated_at']
        ]);
    }
    
    fclose($output);
    exit;
}

function exportJSON($leads) {
    $filename = 'dubai_real_estate_leads_' . date('Y-m-d_H-i-s') . '.json';
    
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    $export_data = [
        'export_date' => date('Y-m-d H:i:s'),
        'total_leads' => count($leads),
        'leads' => $leads
    ];
    
    echo json_encode($export_data, JSON_PRETTY_PRINT);
    exit;
}

function exportExcel($leads) {
    // For Excel export, we'll create a simple HTML table that Excel can open
    $filename = 'dubai_real_estate_leads_' . date('Y-m-d_H-i-s') . '.xls';
    
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    echo '<table border="1">';
    echo '<tr>';
    echo '<th>ID</th>';
    echo '<th>Contact Type</th>';
    echo '<th>Contact Value</th>';
    echo '<th>Author</th>';
    echo '<th>Subreddit</th>';
    echo '<th>Intent Score</th>';
    echo '<th>Status</th>';
    echo '<th>Source Type</th>';
    echo '<th>Source Content</th>';
    echo '<th>Source URL</th>';
    echo '<th>Context</th>';
    echo '<th>Notes</th>';
    echo '<th>Created At</th>';
    echo '<th>Updated At</th>';
    echo '</tr>';
    
    foreach ($leads as $lead) {
        echo '<tr>';
        echo '<td>' . htmlspecialchars($lead['id']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['contact_type']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['contact_value']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['author']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['subreddit']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['intent_score']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['status']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['source_type']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['source_content']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['source_url']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['context']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['notes']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['created_at']) . '</td>';
        echo '<td>' . htmlspecialchars($lead['updated_at']) . '</td>';
        echo '</tr>';
    }
    
    echo '</table>';
    exit;
}

function showExportForm() {
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Get available statuses
    $stmt = $pdo->query("SELECT DISTINCT status FROM leads ORDER BY status");
    $statuses = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Get date range
    $stmt = $pdo->query("SELECT MIN(DATE(created_at)) as min_date, MAX(DATE(created_at)) as max_date FROM leads");
    $dateRange = $stmt->fetch();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Leads - Dubai Real Estate Scraper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-download"></i> Export Leads</h4>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Export Format</label>
                                        <select name="format" class="form-select" required>
                                            <option value="csv">CSV (Comma Separated Values)</option>
                                            <option value="excel">Excel (XLS)</option>
                                            <option value="json">JSON</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status Filter</label>
                                        <select name="status" class="form-select">
                                            <option value="">All Statuses</option>
                                            <?php foreach ($statuses as $status): ?>
                                                <option value="<?= htmlspecialchars($status) ?>"><?= ucfirst($status) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Contact Type</label>
                                        <select name="contact_type" class="form-select">
                                            <option value="">All Types</option>
                                            <option value="phone">Phone</option>
                                            <option value="email">Email</option>
                                            <option value="whatsapp">WhatsApp</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Date Range</label>
                                        <div class="row">
                                            <div class="col-6">
                                                <input type="date" name="date_from" class="form-control" 
                                                       placeholder="From" value="<?= $dateRange['min_date'] ?>">
                                            </div>
                                            <div class="col-6">
                                                <input type="date" name="date_to" class="form-control" 
                                                       placeholder="To" value="<?= $dateRange['max_date'] ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-download"></i> Export Leads
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Export Statistics -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar"></i> Export Statistics</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $totalLeads = $pdo->query("SELECT COUNT(*) FROM leads")->fetchColumn();
                        $newLeads = $pdo->query("SELECT COUNT(*) FROM leads WHERE status = 'new'")->fetchColumn();
                        $contactedLeads = $pdo->query("SELECT COUNT(*) FROM leads WHERE status = 'contacted'")->fetchColumn();
                        $qualifiedLeads = $pdo->query("SELECT COUNT(*) FROM leads WHERE status = 'qualified'")->fetchColumn();
                        ?>
                        
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="h4 text-primary"><?= $totalLeads ?></div>
                                <small>Total Leads</small>
                            </div>
                            <div class="col-3">
                                <div class="h4 text-success"><?= $newLeads ?></div>
                                <small>New</small>
                            </div>
                            <div class="col-3">
                                <div class="h4 text-warning"><?= $contactedLeads ?></div>
                                <small>Contacted</small>
                            </div>
                            <div class="col-3">
                                <div class="h4 text-info"><?= $qualifiedLeads ?></div>
                                <small>Qualified</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php
}
?>
