<?php
/**
 * Clear existing leads and scrape for buyers (not sellers)
 */

require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/RedditScraper.php';

try {
    echo "Clearing existing seller leads and re-scraping for buyers...\n";
    
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Clear existing leads (they were sellers/agents) - in correct order for foreign keys
    $pdo->exec("DELETE FROM keyword_matches");
    $pdo->exec("DELETE FROM leads");
    $pdo->exec("DELETE FROM comments");
    $pdo->exec("DELETE FROM posts");
    $pdo->exec("DELETE FROM scraping_logs");
    
    echo "✅ Cleared existing data\n";
    
    // Now scrape with updated buyer-focused logic
    $scraper = new RedditScraper();
    
    // Target subreddits where people ask for help/advice (more likely to be buyers)
    $buyerSubreddits = ['dubai', 'UAE', 'dubairealestate'];
    $totalLeads = 0;
    
    foreach ($buyerSubreddits as $subreddit) {
        echo "Scraping r/$subreddit for BUYERS...\n";
        try {
            $results = $scraper->scrapeSubreddit($subreddit, 30); // More posts to find buyers
            echo "  Posts: {$results['posts']}, Comments: {$results['comments']}, Leads: {$results['leads']}\n";
            $totalLeads += $results['leads'];
            
            sleep(2); // Longer delay to be respectful
        } catch (Exception $e) {
            echo "  Error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\nTotal BUYER leads found: $totalLeads\n";
    
    // Check what we found
    $stmt = $pdo->query("SELECT COUNT(*) FROM leads");
    $leadCount = $stmt->fetchColumn();
    
    echo "Leads in database: $leadCount\n";
    
    if ($leadCount > 0) {
        echo "\n✅ Found buyer leads! Here are some examples:\n";
        
        $stmt = $pdo->query("SELECT contact_type, contact_value, author, intent_score, context FROM leads ORDER BY intent_score DESC LIMIT 5");
        $sampleLeads = $stmt->fetchAll();
        
        foreach ($sampleLeads as $lead) {
            echo "- {$lead['contact_type']}: {$lead['contact_value']} (Author: {$lead['author']}, Score: {$lead['intent_score']})\n";
            echo "  Context: " . substr($lead['context'], 0, 100) . "...\n\n";
        }
    } else {
        echo "\n⚠️ No buyer leads found. This could mean:\n";
        echo "- Most recent posts are from sellers/agents\n";
        echo "- Need to scrape more posts or different time periods\n";
        echo "- May need to adjust buyer keywords\n";
        echo "\nTry running the scraper on more subreddits or different sorting (hot/top)\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
