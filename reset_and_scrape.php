<?php
/**
 * Reset database and run fresh scrape with simplified logic
 */

require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/RedditScraper.php';

try {
    echo "Resetting database and running fresh scrape...\n";
    
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Clear all data
    $pdo->exec("DELETE FROM keyword_matches");
    $pdo->exec("DELETE FROM leads");
    $pdo->exec("DELETE FROM comments");
    $pdo->exec("DELETE FROM posts");
    $pdo->exec("DELETE FROM scraping_logs");
    
    echo "✅ Database cleared\n";
    
    // Run scraper with simplified logic
    $scraper = new RedditScraper();
    
    echo "Starting fresh scrape...\n";
    $results = $scraper->scrapeSubreddit('dubai', 30); // Start with Dubai subreddit
    
    echo "Dubai results: Posts: {$results['posts']}, Comments: {$results['comments']}, Leads: {$results['leads']}\n";
    
    // Check what we found
    $stmt = $pdo->query("SELECT COUNT(*) FROM leads");
    $leadCount = $stmt->fetchColumn();
    
    echo "Total leads in database: $leadCount\n";
    
    if ($leadCount > 0) {
        echo "\n✅ Found leads! Here are some:\n";
        $stmt = $pdo->query("SELECT contact_type, contact_value, author, intent_score FROM leads LIMIT 3");
        $leads = $stmt->fetchAll();
        
        foreach ($leads as $lead) {
            echo "- {$lead['contact_type']}: {$lead['contact_value']} (Author: {$lead['author']}, Score: {$lead['intent_score']})\n";
        }
    } else {
        echo "\n⚠️ No leads found. Let's try another subreddit...\n";
        
        $results2 = $scraper->scrapeSubreddit('dubairealestate', 20);
        echo "DubaiRealEstate results: Posts: {$results2['posts']}, Comments: {$results2['comments']}, Leads: {$results2['leads']}\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM leads");
        $leadCount = $stmt->fetchColumn();
        echo "Total leads now: $leadCount\n";
    }
    
    echo "\n🎉 Fresh scrape completed!\n";
    echo "Check the web interface at http://localhost:8000/leads.php\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
