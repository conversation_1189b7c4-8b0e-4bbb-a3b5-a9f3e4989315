<?php
/**
 * Fix database to allow 'potential' contact type
 */

require_once 'config/config.php';
require_once 'includes/Database.php';

try {
    echo "Fixing database schema...\n";
    
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // First, let's see the current schema
    $stmt = $pdo->query("PRAGMA table_info(leads)");
    $columns = $stmt->fetchAll();
    
    echo "Current leads table structure:\n";
    foreach ($columns as $col) {
        echo "- {$col['name']}: {$col['type']}\n";
    }
    
    // Create a new table with updated contact_type constraint
    $sql = "
    CREATE TABLE IF NOT EXISTS leads_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        source_type TEXT NOT NULL CHECK(source_type IN ('post', 'comment')),
        source_id TEXT NOT NULL,
        contact_type TEXT NOT NULL CHECK(contact_type IN ('phone', 'email', 'whatsapp', 'potential')),
        contact_value TEXT NOT NULL,
        author TEXT,
        intent_score INTEGER DEFAULT 0,
        context TEXT,
        status TEXT DEFAULT 'new' CHECK(status IN ('new', 'contacted', 'qualified', 'converted', 'rejected')),
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )";
    
    $pdo->exec($sql);
    echo "✅ Created new leads table with 'potential' contact type\n";
    
    // Copy existing data
    $pdo->exec("INSERT INTO leads_new SELECT * FROM leads");
    echo "✅ Copied existing data\n";
    
    // Replace old table
    $pdo->exec("DROP TABLE leads");
    $pdo->exec("ALTER TABLE leads_new RENAME TO leads");
    echo "✅ Replaced old table\n";
    
    echo "\n🎉 Database fixed! Now 'potential' contact type is allowed.\n";
    echo "You can now run the scraper with 'All matching posts' option.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
