<?php
/**
 * Reddit Dubai Real Estate Scraper Configuration
 */

// Database Configuration
define('DB_PATH', __DIR__ . '/../data/reddit_scraper.db');

// Reddit API Configuration
define('REDDIT_CLIENT_ID', 'YNLNhcMEDAxi1ZzyEg');
define('REDDIT_CLIENT_SECRET', 'Nd57LinbaTaCmLbE4Ss9d3m919IDSg');
define('REDDIT_USER_AGENT', 'webscraper/1.0 by sh3llroot');
define('REDDIT_USERNAME', ''); // Your Reddit username (optional for read-only access)
define('REDDIT_PASSWORD', ''); // Your Reddit password (optional for read-only access)

// Target Subreddits
define('TARGET_SUBREDDITS', [
    'dubairealestate',
    'dubai',
    'UAE',
    'DubaiPetrolHeads', // Sometimes has real estate discussions
    'dubai_expats'
]);

// Scraping Configuration
define('POSTS_PER_REQUEST', 25);
define('MAX_POSTS_PER_SUBREDDIT', 100);
define('SCRAPING_INTERVAL_MINUTES', 60); // How often to scrape
define('MAX_COMMENT_DEPTH', 3);

// Contact Information Patterns
define('PHONE_PATTERNS', [
    // UAE numbers
    '/(\+971|971|0)[\s\-]?[0-9]{1,2}[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}/',
    // International format
    '/\+[1-9]\d{1,14}/',
    // WhatsApp mentions
    '/whatsapp[\s:]*(\+?[0-9\s\-]{8,15})/',
    // Common UAE mobile prefixes
    '/(050|055|056|052|054|058)[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}/'
]);

define('EMAIL_PATTERN', '/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/');

// Buying Intent Keywords (BUYERS, not sellers/agents)
define('BUYING_KEYWORDS', [
    'looking to buy',
    'want to buy',
    'planning to buy',
    'interested in buying',
    'first time buyer',
    'need to buy',
    'searching for',
    'looking for apartment',
    'looking for villa',
    'looking for property',
    'need apartment',
    'need villa',
    'need property',
    'property search',
    'apartment hunt',
    'villa hunt',
    'house hunting',
    'home buyer',
    'property buyer',
    'budget is',
    'my budget',
    'can afford',
    'price range',
    'looking to invest',
    'investment property',
    'buy apartment',
    'buy villa',
    'buy property',
    'purchase apartment',
    'purchase villa',
    'relocating to dubai',
    'moving to dubai',
    'new to dubai',
    'just moved',
    'need accommodation',
    'looking to rent',
    'rental budget',
    'monthly budget',
    'seeking apartment',
    'seeking villa',
    'property advice',
    'buying advice',
    'first time in dubai',
    'expat housing',
    'family accommodation'
]);

// Location Keywords (Dubai areas)
define('LOCATION_KEYWORDS', [
    'downtown dubai',
    'dubai marina',
    'jbr',
    'jumeirah beach residence',
    'business bay',
    'difc',
    'dubai hills',
    'arabian ranches',
    'emirates hills',
    'palm jumeirah',
    'dubai creek',
    'city walk',
    'bluewaters',
    'dubai south',
    'dubailand',
    'sports city',
    'motor city',
    'jvc',
    'jumeirah village circle',
    'discovery gardens',
    'international city',
    'dragon mart',
    'silicon oasis',
    'academic city',
    'healthcare city',
    'media city',
    'internet city',
    'knowledge village',
    'tecom',
    'barsha',
    'al furjan',
    'damac hills',
    'akoya',
    'dubai investment park',
    'green community',
    'the springs',
    'the meadows',
    'the lakes',
    'the greens',
    'the views',
    'emirates living',
    'mirdif',
    'festival city',
    'nad al sheba',
    'mohammed bin rashid city',
    'sobha hartland',
    'culture village',
    'al jadaf',
    'business bay',
    'bur dubai',
    'deira',
    'karama',
    'satwa',
    'jumeirah',
    'umm suqeim'
]);

// Scoring Configuration
define('INTENT_SCORE_WEIGHTS', [
    'buying_keywords' => 3,
    'location_keywords' => 2,
    'contact_info' => 5,
    'post_title' => 2,
    'comment_engagement' => 1
]);

define('MIN_INTENT_SCORE', 3); // Minimum score to consider as a lead (lowered to catch more)

// Keywords that indicate SELLERS/AGENTS (to exclude)
define('SELLER_KEYWORDS', [
    'for sale',
    'selling',
    'available for sale',
    'property for sale',
    'apartment for sale',
    'villa for sale',
    'off plan',
    'ready property',
    'handover',
    'developer',
    'real estate agent',
    'broker',
    'commission',
    'listing',
    'property consultant',
    'sales consultant',
    'connect with me',
    'contact me',
    'call me',
    'whatsapp me',
    'dm me',
    'message me',
    'i have',
    'we have',
    'available units',
    'units available',
    'best price',
    'good deal',
    'investment opportunity',
    'roi',
    'rental yield',
    'payment plan',
    'installment',
    'down payment',
    'booking',
    'reserve',
    'limited time',
    'offer',
    'promotion',
    'discount',
    'aed only',
    'starting from',
    'price starts',
    'competitive price'
]);

// Application Settings
define('TIMEZONE', 'Asia/Dubai');
define('DATE_FORMAT', 'Y-m-d H:i:s');
define('RESULTS_PER_PAGE', 50);

// Error Reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set(TIMEZONE);

// Auto-create data directory
$dataDir = dirname(DB_PATH);
if (!is_dir($dataDir)) {
    mkdir($dataDir, 0755, true);
}
