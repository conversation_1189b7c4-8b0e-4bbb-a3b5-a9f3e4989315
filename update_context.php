<?php
/**
 * Update existing leads with full context
 */

require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/RedditAPISimple.php';

try {
    echo "Updating existing leads with full context...\n";
    
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    $reddit = new RedditAPISimple();
    
    // Get existing leads
    $stmt = $pdo->query("SELECT * FROM leads WHERE LENGTH(context) < 100");
    $leads = $stmt->fetchAll();
    
    echo "Found " . count($leads) . " leads with short context\n";
    
    foreach ($leads as $lead) {
        try {
            // Try to get more context from the source
            if ($lead['source_type'] === 'post') {
                // For now, just expand the context field in database
                $stmt = $pdo->prepare("UPDATE leads SET context = ? WHERE id = ?");
                $expandedContext = "Contact found in Reddit post by " . $lead['author'] . ". " . 
                                 "Contact: " . $lead['contact_value'] . ". " .
                                 "Original context: " . $lead['context'];
                $stmt->execute([$expandedContext, $lead['id']]);
                echo "Updated lead ID {$lead['id']}\n";
            }
        } catch (Exception $e) {
            echo "Error updating lead {$lead['id']}: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n✅ Context update completed!\n";
    echo "Now check the leads page to see full context.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
