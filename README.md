# Reddit Dubai Real Estate Scraper

A PHP web application that scrapes Reddit for people looking to buy apartments or villas in Dubai, extracting their contact information and analyzing buying intent.

## Features

- **Reddit API Integration**: Scrapes posts and comments from Dubai real estate subreddits
- **Contact Information Extraction**: Finds phone numbers, emails, and WhatsApp contacts
- **Buying Intent Analysis**: Scores content based on keywords and context
- **Web Dashboard**: View, filter, and manage leads through a modern interface
- **Export Functionality**: Export leads to CSV, Excel, or JSON formats
- **Automated Scraping**: Cron job support for regular data collection
- **SQLite Database**: Lightweight database for storing leads and scraping logs

## Target Subreddits

- r/dubairealestate
- r/dubai
- r/UAE
- r/DubaiPetrolHeads
- r/dubai_expats

## Requirements

- PHP 7.4 or higher
- SQLite3 extension
- cURL extension
- Reddit API credentials

## Installation

1. **Clone or download** this project to your web server directory

2. **Set up Reddit API credentials**:
   - Go to [Reddit Apps](https://www.reddit.com/prefs/apps)
   - Create a new app (choose "script" type)
   - Note down your Client ID and Client Secret

3. **Run the setup**:
   - Navigate to `setup.php` in your browser
   - Follow the setup wizard to configure Reddit API credentials
   - Test the connection to ensure everything works

4. **Start scraping**:
   - Go to `scraper.php` to run manual scraping
   - Or visit `index.php` for the main dashboard

## Configuration

Edit `config/config.php` to customize:

- Reddit API credentials
- Target subreddits
- Scraping intervals
- Intent scoring weights
- Contact information patterns

## Usage

### Manual Scraping

1. Visit `scraper.php`
2. Click "Run Full Scraper" or select individual subreddits
3. Monitor progress and view logs

### Dashboard

1. Visit `index.php`
2. View all leads with filtering options
3. Update lead statuses and add notes
4. Export leads in various formats

### Automated Scraping

Add to your crontab for automated scraping:

```bash
# Run every hour
0 * * * * /usr/bin/php /path/to/your/project/cron_scraper.php

# Or run every 30 minutes
*/30 * * * * /usr/bin/php /path/to/your/project/cron_scraper.php
```

## Contact Information Detection

The scraper detects:

### Phone Numbers
- UAE mobile numbers: +971 50/55/56/52/54/58 XXX XXXX
- International numbers with country codes
- Various formatting styles (spaces, dashes, parentheses)

### Email Addresses
- Standard email formats
- Obfuscated emails (using [at] and [dot])

### WhatsApp Numbers
- Numbers mentioned with "WhatsApp", "WA", or similar keywords

## Buying Intent Analysis

The system scores content based on:

### High-Value Keywords (3-4 points)
- "looking to buy"
- "ready to buy"
- "first time buyer"
- "cash buyer"

### Location Keywords (1-2 points)
- Dubai areas: Downtown, Marina, JBR, Business Bay, etc.

### Urgency Indicators (1-3 points)
- "urgent", "ASAP", "immediately"
- "this month", "soon"

### Budget Indicators (1-3 points)
- Specific budget mentions
- Price ranges
- Financing terms

### Contact Information (5 points)
- Presence of phone numbers or emails

## Database Schema

### Tables
- `leads`: Contact information and intent scores
- `posts`: Reddit posts data
- `comments`: Reddit comments data
- `subreddits`: Subreddit information and scraping status
- `keyword_matches`: Keyword analysis results
- `scraping_logs`: Scraping activity logs

## File Structure

```
├── config/
│   └── config.php              # Configuration settings
├── includes/
│   ├── Database.php            # Database management
│   ├── RedditAPI.php           # Reddit API client
│   ├── ContactExtractor.php    # Contact information extraction
│   ├── IntentAnalyzer.php      # Buying intent analysis
│   └── RedditScraper.php       # Main scraper class
├── data/                       # SQLite database storage
├── logs/                       # Scraping logs
├── index.php                   # Main dashboard
├── scraper.php                 # Scraper control panel
├── export.php                  # Export functionality
├── setup.php                   # Setup wizard
├── cron_scraper.php           # Automated scraper
└── README.md                   # This file
```

## API Rate Limits

The scraper respects Reddit's API rate limits:
- 60 requests per minute for OAuth
- 1-second delays between requests
- Incremental scraping to avoid hitting limits

## Security Considerations

- Store Reddit credentials securely
- Restrict access to the admin interface
- Regularly backup your database
- Monitor scraping logs for errors

## Troubleshooting

### Common Issues

1. **"Reddit API credentials not configured"**
   - Run `setup.php` and configure your Reddit API credentials

2. **"Failed to get Reddit access token"**
   - Check your Client ID and Client Secret
   - Ensure your Reddit app is configured correctly

3. **"Database connection failed"**
   - Check file permissions on the `data/` directory
   - Ensure SQLite3 extension is installed

4. **No leads found**
   - Check if the target subreddits have recent activity
   - Adjust intent scoring thresholds in config
   - Review keyword patterns

### Debug Mode

Enable debug mode in `config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Contributing

To extend the scraper:

1. Add new subreddits to `TARGET_SUBREDDITS` in config
2. Customize keyword patterns for different markets
3. Add new contact information patterns
4. Implement email notifications for new leads

## Legal Compliance

- Respect Reddit's Terms of Service
- Use scraped data responsibly
- Comply with local privacy laws
- Don't spam or harass users

## Support

For issues or questions:
1. Check the scraping logs in `logs/`
2. Review the setup configuration
3. Test Reddit API connectivity
4. Check file permissions and PHP extensions

---

**Note**: This tool is for legitimate business purposes only. Always respect user privacy and platform terms of service.
