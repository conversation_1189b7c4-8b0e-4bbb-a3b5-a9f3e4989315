<?php
/**
 * Simple leads display page (no AJAX)
 */

require_once 'config/config.php';
require_once 'includes/Database.php';

$db = Database::getInstance();
$pdo = $db->getPDO();

// Get leads
try {
    $stmt = $pdo->query("SELECT * FROM leads ORDER BY created_at DESC LIMIT 20");
    $leads = $stmt->fetchAll();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM leads");
    $totalLeads = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM posts");
    $totalPosts = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM comments");
    $totalComments = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dubai Real Estate Leads</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .lead-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .contact-info {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1><i class="fas fa-home"></i> Dubai Real Estate Leads</h1>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <h5>Error:</h5>
                        <p><?= htmlspecialchars($error) ?></p>
                    </div>
                <?php else: ?>
                    
                    <!-- Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-primary"><?= $totalLeads ?></h3>
                                    <p>Total Leads</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-success"><?= $totalPosts ?></h3>
                                    <p>Posts Scraped</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-info"><?= $totalComments ?></h3>
                                    <p>Comments Processed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Leads -->
                    <h3>Recent Leads</h3>
                    
                    <?php if (empty($leads)): ?>
                        <div class="alert alert-info">
                            <h5>No leads found</h5>
                            <p>Run the scraper to find leads from Reddit.</p>
                            <a href="scraper.php" class="btn btn-primary">Go to Scraper</a>
                        </div>
                    <?php else: ?>
                        
                        <?php foreach ($leads as $lead): ?>
                            <div class="card lead-card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="contact-info me-3">
                                                    <?php if ($lead['contact_type'] === 'phone'): ?>
                                                        <i class="fas fa-phone"></i>
                                                    <?php elseif ($lead['contact_type'] === 'email'): ?>
                                                        <i class="fas fa-envelope"></i>
                                                    <?php else: ?>
                                                        <i class="fab fa-whatsapp"></i>
                                                    <?php endif; ?>
                                                    <?= htmlspecialchars($lead['contact_value']) ?>
                                                </span>
                                                <span class="badge bg-success">New</span>
                                                <span class="badge bg-primary ms-2">Score: <?= $lead['intent_score'] ?></span>
                                            </div>
                                            <h6 class="card-title">
                                                <i class="fas fa-user"></i> <?= htmlspecialchars($lead['author'] ?: 'Unknown') ?>
                                            </h6>
                                            <p class="card-text small">
                                                <?= htmlspecialchars(substr($lead['context'] ?: 'No context available', 0, 200)) ?>
                                                <?php if (strlen($lead['context'] ?: '') > 200): ?>...<?php endif; ?>
                                            </p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <small class="text-muted"><?= date('M j, Y H:i', strtotime($lead['created_at'])) ?></small><br>
                                            <span class="badge bg-secondary"><?= ucfirst($lead['source_type']) ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        
                    <?php endif; ?>
                    
                <?php endif; ?>
                
                <div class="mt-4">
                    <a href="scraper.php" class="btn btn-primary">
                        <i class="fas fa-robot"></i> Run Scraper
                    </a>
                    <a href="export.php" class="btn btn-success">
                        <i class="fas fa-download"></i> Export Leads
                    </a>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-dashboard"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
