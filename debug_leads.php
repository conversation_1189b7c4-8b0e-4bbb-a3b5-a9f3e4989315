<?php
/**
 * Debug the leads loading issue
 */

require_once 'config/config.php';
require_once 'includes/Database.php';

$db = Database::getInstance();
$pdo = $db->getPDO();

echo "Debugging leads loading...\n";

// Test simple query first
try {
    $stmt = $pdo->query("SELECT * FROM leads LIMIT 5");
    $leads = $stmt->fetchAll();
    echo "✅ Simple query works. Found " . count($leads) . " leads\n";
    
    if (!empty($leads)) {
        echo "Sample lead: " . print_r($leads[0], true) . "\n";
    }
} catch (Exception $e) {
    echo "❌ Simple query failed: " . $e->getMessage() . "\n";
}

// Test the complex query from index.php
try {
    $sql = "SELECT l.*, 
                   COALESCE(p.title, SUBSTR(c.content, 1, 100), 'No content') as source_content,
                   COALESCE(s.name, 'unknown') as subreddit
            FROM leads l
            LEFT JOIN posts p ON l.source_type = 'post' AND l.source_id = p.id
            LEFT JOIN comments c ON l.source_type = 'comment' AND l.source_id = c.id
            LEFT JOIN subreddits s ON (p.subreddit_id = s.id OR c.post_id IN (SELECT id FROM posts WHERE subreddit_id = s.id))
            ORDER BY l.created_at DESC
            LIMIT 5";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $leads = $stmt->fetchAll();
    
    echo "✅ Complex query works. Found " . count($leads) . " leads\n";
    
    if (!empty($leads)) {
        echo "Sample complex lead: " . print_r($leads[0], true) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Complex query failed: " . $e->getMessage() . "\n";
    
    // Try simpler version
    echo "Trying simpler query...\n";
    try {
        $sql = "SELECT l.*, 'No content' as source_content, 'unknown' as subreddit
                FROM leads l
                ORDER BY l.created_at DESC
                LIMIT 5";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $leads = $stmt->fetchAll();
        
        echo "✅ Simplified query works. Found " . count($leads) . " leads\n";
        
    } catch (Exception $e2) {
        echo "❌ Even simplified query failed: " . $e2->getMessage() . "\n";
    }
}

// Check table structure
echo "\nChecking table structures...\n";

try {
    $stmt = $pdo->query("PRAGMA table_info(leads)");
    $columns = $stmt->fetchAll();
    echo "Leads table columns:\n";
    foreach ($columns as $col) {
        echo "  - {$col['name']} ({$col['type']})\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking leads table: " . $e->getMessage() . "\n";
}

try {
    $stmt = $pdo->query("PRAGMA table_info(posts)");
    $columns = $stmt->fetchAll();
    echo "Posts table columns:\n";
    foreach ($columns as $col) {
        echo "  - {$col['name']} ({$col['type']})\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking posts table: " . $e->getMessage() . "\n";
}
?>
