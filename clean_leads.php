<?php
/**
 * Clean false positive leads and run fresh scrape
 */

require_once 'config/config.php';
require_once 'includes/Database.php';

try {
    echo "Cleaning false positive leads...\n";
    
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Delete false positive leads
    $falsePositives = [
        '20122015', '20152019', '2015', '2019',
        '+39', '+450', '+3000', '39', '450', '3000',
        '25052059', '42670336', '97059460', '49761839'
    ];
    
    $deletedCount = 0;
    foreach ($falsePositives as $fp) {
        $stmt = $pdo->prepare("DELETE FROM leads WHERE contact_value = ?");
        $stmt->execute([$fp]);
        $deletedCount += $stmt->rowCount();
    }
    
    echo "✅ Deleted $deletedCount false positive leads\n";
    
    // Check remaining leads
    $stmt = $pdo->query("SELECT COUNT(*) FROM leads");
    $remainingLeads = $stmt->fetchColumn();
    
    echo "Remaining leads: $remainingLeads\n";
    
    if ($remainingLeads > 0) {
        echo "\nRemaining leads:\n";
        $stmt = $pdo->query("SELECT contact_type, contact_value, author FROM leads LIMIT 5");
        $leads = $stmt->fetchAll();
        
        foreach ($leads as $lead) {
            echo "- {$lead['contact_type']}: {$lead['contact_value']} (Author: {$lead['author']})\n";
        }
    }
    
    echo "\n✅ Database cleaned! The improved contact extraction will now filter out false positives.\n";
    echo "Go to the scraper and run it again - it should only find real phone numbers now.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
