<?php
/**
 * Test the full scraper functionality
 */

require_once 'config/config.php';
require_once 'includes/RedditScraper.php';

try {
    echo "Testing Reddit Scraper...\n";
    
    $scraper = new RedditScraper();
    
    // Test scraping a small amount from Dubai subreddit
    echo "Scraping r/dubai (limited to 5 posts)...\n";
    $results = $scraper->scrapeSubreddit('dubai', 5);
    
    echo "✅ Scraping completed!\n";
    echo "Posts processed: " . $results['posts'] . "\n";
    echo "Comments processed: " . $results['comments'] . "\n";
    echo "Leads found: " . $results['leads'] . "\n";
    
    if (!empty($results['errors'])) {
        echo "Errors encountered:\n";
        foreach ($results['errors'] as $error) {
            echo "  - $error\n";
        }
    }
    
    // Get stats
    echo "\nGetting scraper statistics...\n";
    $stats = $scraper->getStats();
    echo "Total posts in database: " . $stats['total_posts'] . "\n";
    echo "Total comments in database: " . $stats['total_comments'] . "\n";
    echo "Total leads in database: " . $stats['total_leads'] . "\n";
    
    if ($stats['total_leads'] > 0) {
        echo "\n🎉 Found leads! Check the dashboard to view them.\n";
    } else {
        echo "\nNo leads found in this batch. Try running more subreddits or check different time periods.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Scraper test failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ Scraper is working correctly!\n";
echo "You can now use the web interface to run full scraping sessions.\n";
?>
