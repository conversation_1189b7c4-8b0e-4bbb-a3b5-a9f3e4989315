<?php
/**
 * Quick scrape to get some real data
 */

require_once 'config/config.php';
require_once 'includes/RedditScraper.php';

try {
    echo "Running quick scrape to get real data...\n";
    
    $scraper = new RedditScraper();
    
    // Scrape multiple subreddits to find leads
    $subreddits = ['dubairealestate', 'dubai', 'UAE'];
    $totalLeads = 0;
    
    foreach ($subreddits as $subreddit) {
        echo "Scraping r/$subreddit...\n";
        try {
            $results = $scraper->scrapeSubreddit($subreddit, 20); // 20 posts each
            echo "  Posts: {$results['posts']}, Comments: {$results['comments']}, Leads: {$results['leads']}\n";
            $totalLeads += $results['leads'];
            
            // Small delay between subreddits
            sleep(1);
        } catch (Exception $e) {
            echo "  Error: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\nTotal leads found: $totalLeads\n";
    
    // Check database for leads
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM leads");
    $leadCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM posts");
    $postCount = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM comments");
    $commentCount = $stmt->fetchColumn();
    
    echo "\nDatabase totals:\n";
    echo "Posts: $postCount\n";
    echo "Comments: $commentCount\n";
    echo "Leads: $leadCount\n";
    
    if ($leadCount > 0) {
        echo "\n✅ Found leads! The dashboard should now show data.\n";
        
        // Show sample leads
        $stmt = $pdo->query("SELECT contact_type, contact_value, author, intent_score FROM leads LIMIT 5");
        $sampleLeads = $stmt->fetchAll();
        
        echo "\nSample leads:\n";
        foreach ($sampleLeads as $lead) {
            echo "- {$lead['contact_type']}: {$lead['contact_value']} (Author: {$lead['author']}, Score: {$lead['intent_score']})\n";
        }
    } else {
        echo "\n⚠️ No leads found yet. This could mean:\n";
        echo "- Recent posts don't contain contact information\n";
        echo "- Posts don't meet the intent scoring threshold\n";
        echo "- Need to scrape more posts or different time periods\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
