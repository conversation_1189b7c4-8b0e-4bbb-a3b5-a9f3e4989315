<?php
/**
 * Debug Reddit API Authentication
 */

require_once 'config/config.php';

echo "=== Reddit API Debug ===\n";
echo "Client ID: " . REDDIT_CLIENT_ID . "\n";
echo "Client Secret: " . substr(REDDIT_CLIENT_SECRET, 0, 10) . "...\n";
echo "User Agent: " . REDDIT_USER_AGENT . "\n\n";

// Test authentication manually
$clientId = REDDIT_CLIENT_ID;
$clientSecret = REDDIT_CLIENT_SECRET;
$userAgent = REDDIT_USER_AGENT;

$authUrl = 'https://www.reddit.com/api/v1/access_token';

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $authUrl,
    CURLOPT_POST => true,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'User-Agent: ' . $userAgent,
        'Authorization: Basic ' . base64_encode($clientId . ':' . $clientSecret)
    ],
    CURLOPT_POSTFIELDS => 'grant_type=client_credentials',
    CURLOPT_TIMEOUT => 30,
    CURLOPT_VERBOSE => false
]);

echo "Making authentication request...\n";
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($curlError) {
    echo "cURL Error: $curlError\n";
}

echo "Response: $response\n\n";

if ($httpCode === 200) {
    $data = json_decode($response, true);
    if ($data && isset($data['access_token'])) {
        echo "✅ Authentication successful!\n";
        echo "Access token: " . substr($data['access_token'], 0, 20) . "...\n";
        echo "Token type: " . ($data['token_type'] ?? 'unknown') . "\n";
        echo "Expires in: " . ($data['expires_in'] ?? 'unknown') . " seconds\n";
        
        // Test making an API call
        echo "\nTesting API call...\n";
        $apiUrl = 'https://oauth.reddit.com/r/test/hot?limit=1';
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'User-Agent: ' . $userAgent,
                'Authorization: Bearer ' . $data['access_token']
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        
        $apiResponse = curl_exec($ch);
        $apiHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "API HTTP Code: $apiHttpCode\n";
        if ($apiHttpCode === 200) {
            $apiData = json_decode($apiResponse, true);
            if ($apiData && isset($apiData['data']['children'])) {
                echo "✅ API call successful! Found " . count($apiData['data']['children']) . " posts\n";
            } else {
                echo "❌ API call failed - invalid response format\n";
            }
        } else {
            echo "❌ API call failed with HTTP code: $apiHttpCode\n";
            echo "Response: " . substr($apiResponse, 0, 200) . "\n";
        }
        
    } else {
        echo "❌ Authentication failed - invalid response format\n";
        echo "Response data: " . print_r($data, true) . "\n";
    }
} else {
    echo "❌ Authentication failed with HTTP code: $httpCode\n";
    
    if ($httpCode === 401) {
        echo "\n🔍 Troubleshooting 401 Unauthorized:\n";
        echo "1. Check if Client ID and Secret are correct\n";
        echo "2. Verify the Reddit app is configured as 'script' type\n";
        echo "3. Make sure the app is not deleted or suspended\n";
        echo "4. Check if the credentials have special characters that need encoding\n";
    }
}
?>
