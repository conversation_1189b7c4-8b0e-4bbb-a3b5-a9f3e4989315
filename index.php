<?php
require_once 'config/config.php';
require_once 'includes/Database.php';

$db = Database::getInstance();
$pdo = $db->getPDO();

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_GET['action']) {
            case 'get_leads':
            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? RESULTS_PER_PAGE);
            $status = $_GET['status'] ?? '';
            $search = $_GET['search'] ?? '';
            $leadType = $_GET['lead_type'] ?? '';

            $offset = ($page - 1) * $limit;

            $where = [];
            $params = [];

            if ($status) {
                $where[] = "l.status = ?";
                $params[] = $status;
            }

            if ($search) {
                $where[] = "(l.contact_value LIKE ? OR l.author LIKE ? OR l.context LIKE ?)";
                $params[] = "%$search%";
                $params[] = "%$search%";
                $params[] = "%$search%";
            }

            if ($leadType) {
                if ($leadType === 'contact') {
                    $where[] = "l.contact_type IN ('phone', 'email', 'whatsapp')";
                } elseif ($leadType === 'potential') {
                    $where[] = "l.contact_type = 'potential'";
                }
            }

            $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';
            
            $sql = "SELECT l.*,
                           'Reddit Post/Comment' as source_content,
                           'Dubai Subreddit' as subreddit
                    FROM leads l
                    $whereClause
                    ORDER BY l.created_at DESC
                    LIMIT $limit OFFSET $offset";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $leads = $stmt->fetchAll();
            
            // Get total count
            $countSql = "SELECT COUNT(*) FROM leads l $whereClause";
            $countStmt = $pdo->prepare($countSql);
            $countStmt->execute($params);
            $total = $countStmt->fetchColumn();
            
            echo json_encode([
                'leads' => $leads,
                'total' => $total,
                'page' => $page,
                'pages' => ceil($total / $limit)
            ]);
            exit;
            
        case 'update_lead_status':
            $id = (int)$_POST['id'];
            $status = $_POST['status'];
            $notes = $_POST['notes'] ?? '';
            
            $sql = "UPDATE leads SET status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $success = $stmt->execute([$status, $notes, $id]);
            
            echo json_encode(['success' => $success]);
            exit;
            
        case 'get_stats':
            $stats = [];
            
            // Total counts
            $stats['total_posts'] = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
            $stats['total_comments'] = $pdo->query("SELECT COUNT(*) FROM comments")->fetchColumn();
            $stats['total_leads'] = $pdo->query("SELECT COUNT(*) FROM leads")->fetchColumn();
            
            // Leads by status
            $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM leads GROUP BY status");
            $stats['leads_by_status'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            // Recent activity (last 7 days)
            $stmt = $pdo->query("SELECT DATE(created_at) as date, COUNT(*) as count FROM leads WHERE created_at >= date('now', '-7 days') GROUP BY DATE(created_at) ORDER BY date DESC");
            $stats['recent_activity'] = $stmt->fetchAll();
            
            // Top subreddits
            $stmt = $pdo->query("
                SELECT s.name, COUNT(l.id) as lead_count 
                FROM subreddits s 
                LEFT JOIN posts p ON s.id = p.subreddit_id 
                LEFT JOIN leads l ON (l.source_type = 'post' AND l.source_id = p.id)
                GROUP BY s.id, s.name 
                ORDER BY lead_count DESC 
                LIMIT 5
            ");
            $stats['top_subreddits'] = $stmt->fetchAll();
            
            echo json_encode($stats);
            exit;

        case 'get_lead_details':
            $leadId = (int)($_GET['id'] ?? 0);
            if (!$leadId) {
                echo json_encode(['success' => false, 'error' => 'Invalid lead ID']);
                exit;
            }

            $stmt = $pdo->prepare("SELECT * FROM leads WHERE id = ?");
            $stmt->execute([$leadId]);
            $lead = $stmt->fetch();

            if (!$lead) {
                echo json_encode(['success' => false, 'error' => 'Lead not found']);
                exit;
            }

            echo json_encode(['success' => true, 'lead' => $lead]);
            exit;

        case 'update_lead_status':
            $leadId = (int)($_GET['id'] ?? 0);
            $status = $_GET['status'] ?? '';

            if (!$leadId || !$status) {
                echo json_encode(['success' => false, 'error' => 'Invalid parameters']);
                exit;
            }

            $stmt = $pdo->prepare("UPDATE leads SET status = ? WHERE id = ?");
            $result = $stmt->execute([$status, $leadId]);

            echo json_encode(['success' => $result]);
            exit;

        case 'save_lead_notes':
            $leadId = (int)($_GET['id'] ?? 0);
            $notes = $_POST['notes'] ?? '';

            if (!$leadId) {
                echo json_encode(['success' => false, 'error' => 'Invalid lead ID']);
                exit;
            }

            $stmt = $pdo->prepare("UPDATE leads SET notes = ? WHERE id = ?");
            $result = $stmt->execute([$notes, $leadId]);

            echo json_encode(['success' => $result]);
            exit;
    }

    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit Dubai Real Estate Scraper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .lead-card {
            border-left: 4px solid #007bff;
            margin-bottom: 1rem;
        }
        .lead-card.high-intent {
            border-left-color: #28a745;
        }
        .lead-card.medium-intent {
            border-left-color: #ffc107;
        }
        .lead-card.low-intent {
            border-left-color: #dc3545;
        }
        .contact-info {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }
        .intent-score {
            font-weight: bold;
        }
        .status-badge {
            font-size: 0.8rem;
        }
        .sidebar {
            background: #f8f9fa;
            min-height: 100vh;
            padding: 1rem;
        }
        .main-content {
            padding: 1rem;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar">
                <h4><i class="fas fa-home"></i> Dubai Real Estate Leads</h4>
                <hr>
                
                <!-- Stats -->
                <div class="card stats-card mb-3">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-chart-bar"></i> Statistics</h6>
                        <div id="stats-content">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i> Loading...
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6><i class="fas fa-filter"></i> Filters</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Lead Type</label>
                            <select class="form-select" id="lead-type-filter">
                                <option value="">All Leads</option>
                                <option value="contact">With Contact Info</option>
                                <option value="potential">Potential Leads</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select class="form-select" id="status-filter">
                                <option value="">All Statuses</option>
                                <option value="new">New</option>
                                <option value="contacted">Contacted</option>
                                <option value="qualified">Qualified</option>
                                <option value="not_interested">Not Interested</option>
                                <option value="invalid">Invalid</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Search</label>
                            <input type="text" class="form-control" id="search-input" placeholder="Search contacts, authors...">
                        </div>
                        
                        <button class="btn btn-primary w-100" onclick="loadLeads()">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h6><i class="fas fa-cogs"></i> Actions</h6>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success w-100 mb-2" onclick="runScraper()">
                            <i class="fas fa-play"></i> Run Scraper
                        </button>
                        <button class="btn btn-info w-100 mb-2" onclick="exportLeads()">
                            <i class="fas fa-download"></i> Export Leads
                        </button>
                        <button class="btn btn-warning w-100" onclick="viewLogs()">
                            <i class="fas fa-list"></i> View Logs
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users"></i> Real Estate Leads</h2>
                    <div>
                        <button class="btn btn-outline-primary" onclick="loadLeads()">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                </div>
                
                <!-- Leads Container -->
                <div id="leads-container">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading leads...
                    </div>
                </div>
                
                <!-- Pagination -->
                <nav id="pagination-container" style="display: none;">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>
    
    <!-- Lead Details Modal -->
    <div class="modal fade" id="leadModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Lead Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="lead-details">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" onclick="updateLeadStatus()">Update Status</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPage = 1;
        let currentLead = null;
        
        // Load leads on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadLeads();
        });
        
        function loadStats() {
            fetch('?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    const statsHtml = `
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="h4">${data.total_leads}</div>
                                <small>Total Leads</small>
                            </div>
                            <div class="col-4">
                                <div class="h4">${data.total_posts}</div>
                                <small>Posts</small>
                            </div>
                            <div class="col-4">
                                <div class="h4">${data.total_comments}</div>
                                <small>Comments</small>
                            </div>
                        </div>
                        <hr>
                        <div class="small">
                            <strong>Status Breakdown:</strong><br>
                            ${Object.entries(data.leads_by_status || {}).map(([status, count]) => 
                                `${status}: ${count}`
                            ).join('<br>')}
                        </div>
                    `;
                    document.getElementById('stats-content').innerHTML = statsHtml;
                })
                .catch(error => {
                    document.getElementById('stats-content').innerHTML = '<div class="text-danger">Error loading stats</div>';
                });
        }
        
        function loadLeads(page = 1) {
            currentPage = page;
            const status = document.getElementById('status-filter').value;
            const search = document.getElementById('search-input').value;
            const leadType = document.getElementById('lead-type-filter').value;

            document.getElementById('leads-container').innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading leads...</div>';

            const params = new URLSearchParams({
                action: 'get_leads',
                page: page,
                status: status,
                search: search,
                lead_type: leadType
            });
            
            fetch('?' + params)
                .then(response => response.json())
                .then(data => {
                    displayLeads(data.leads);
                    displayPagination(data.page, data.pages, data.total);
                })
                .catch(error => {
                    document.getElementById('leads-container').innerHTML = '<div class="alert alert-danger">Error loading leads</div>';
                });
        }
        
        function displayLeads(leads) {
            if (leads.length === 0) {
                document.getElementById('leads-container').innerHTML = '<div class="alert alert-info">No leads found</div>';
                return;
            }
            
            const leadsHtml = leads.map(lead => {
                const intentClass = lead.intent_score >= 15 ? 'high-intent' : 
                                  lead.intent_score >= 8 ? 'medium-intent' : 'low-intent';
                
                const statusBadge = getStatusBadge(lead.status);
                const contactIcon = getContactIcon(lead.contact_type);
                
                return `
                    <div class="card lead-card ${intentClass}">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="contact-info me-3">
                                            ${contactIcon} ${lead.contact_value}
                                        </span>
                                        ${statusBadge}
                                        <span class="badge bg-primary ms-2">Score: ${lead.intent_score}</span>
                                    </div>
                                    <h6 class="card-title">
                                        <i class="fas fa-user"></i> ${lead.author || 'Unknown'}
                                        <small class="text-muted">• ${lead.subreddit || 'Unknown'}</small>
                                    </h6>
                                    <p class="card-text small">${truncateText(lead.context || '', 150)}</p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <small class="text-muted">${formatDate(lead.created_at)}</small><br>
                                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="viewLead(${lead.id})">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('leads-container').innerHTML = leadsHtml;
        }
        
        function displayPagination(currentPage, totalPages, totalItems) {
            if (totalPages <= 1) {
                document.getElementById('pagination-container').style.display = 'none';
                return;
            }
            
            document.getElementById('pagination-container').style.display = 'block';
            
            let paginationHtml = '';
            
            // Previous button
            if (currentPage > 1) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadLeads(${currentPage - 1})">Previous</a></li>`;
            }
            
            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const active = i === currentPage ? 'active' : '';
                paginationHtml += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadLeads(${i})">${i}</a></li>`;
            }
            
            // Next button
            if (currentPage < totalPages) {
                paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadLeads(${currentPage + 1})">Next</a></li>`;
            }
            
            document.getElementById('pagination').innerHTML = paginationHtml;
        }
        
        function getStatusBadge(status) {
            const badges = {
                'new': '<span class="badge bg-success status-badge">New</span>',
                'contacted': '<span class="badge bg-warning status-badge">Contacted</span>',
                'qualified': '<span class="badge bg-info status-badge">Qualified</span>',
                'not_interested': '<span class="badge bg-secondary status-badge">Not Interested</span>',
                'invalid': '<span class="badge bg-danger status-badge">Invalid</span>'
            };
            return badges[status] || '<span class="badge bg-light status-badge">Unknown</span>';
        }
        
        function getContactIcon(type) {
            const icons = {
                'phone': '<i class="fas fa-phone"></i>',
                'email': '<i class="fas fa-envelope"></i>',
                'whatsapp': '<i class="fab fa-whatsapp"></i>'
            };
            return icons[type] || '<i class="fas fa-contact-card"></i>';
        }
        
        function truncateText(text, length) {
            return text.length > length ? text.substring(0, length) + '...' : text;
        }
        
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
        }
        
        function viewLead(id) {
            // Fetch lead details
            fetch(`?action=get_lead_details&id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showLeadModal(data.lead);
                    } else {
                        alert('Error loading lead details: ' + data.error);
                    }
                })
                .catch(error => {
                    alert('Network error: ' + error.message);
                });
        }

        function showLeadModal(lead) {
            const modalHtml = `
                <div class="modal fade" id="leadModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-user"></i> Lead Details - ${lead.author}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-phone"></i> Contact Information</h6>
                                        <div class="bg-light p-3 rounded mb-3">
                                            <strong>${lead.contact_type.toUpperCase()}:</strong><br>
                                            <span class="h5 text-primary">${lead.contact_value}</span>
                                        </div>

                                        <h6><i class="fas fa-info-circle"></i> Lead Info</h6>
                                        <ul class="list-unstyled">
                                            <li><strong>Author:</strong> ${lead.author}</li>
                                            <li><strong>Intent Score:</strong> <span class="badge bg-primary">${lead.intent_score}</span></li>
                                            <li><strong>Status:</strong> <span class="badge bg-success">${lead.status}</span></li>
                                            <li><strong>Source:</strong> ${lead.source_type}</li>
                                            <li><strong>Found:</strong> ${new Date(lead.created_at).toLocaleString()}</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fas fa-comment"></i> Full Context</h6>
                                        <div class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">
                                            ${lead.context ? lead.context.replace(/\n/g, '<br>') : 'No context available'}
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <h6><i class="fas fa-sticky-note"></i> Notes</h6>
                                    <textarea class="form-control" id="leadNotes" rows="3" placeholder="Add notes about this lead...">${lead.notes || ''}</textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-success" onclick="updateLeadStatus(${lead.id}, 'contacted')">
                                    <i class="fas fa-phone"></i> Mark as Contacted
                                </button>
                                <button type="button" class="btn btn-warning" onclick="updateLeadStatus(${lead.id}, 'qualified')">
                                    <i class="fas fa-star"></i> Mark as Qualified
                                </button>
                                <button type="button" class="btn btn-primary" onclick="saveLeadNotes(${lead.id})">
                                    <i class="fas fa-save"></i> Save Notes
                                </button>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('leadModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('leadModal'));
            modal.show();
        }

        function updateLeadStatus(leadId, status) {
            fetch(`?action=update_lead_status&id=${leadId}&status=${status}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Lead status updated to: ${status}`);
                    // Close modal and refresh leads
                    const modal = bootstrap.Modal.getInstance(document.getElementById('leadModal'));
                    modal.hide();
                    loadLeads();
                } else {
                    alert('Error updating status: ' + data.error);
                }
            })
            .catch(error => {
                alert('Network error: ' + error.message);
            });
        }

        function saveLeadNotes(leadId) {
            const notes = document.getElementById('leadNotes').value;

            fetch(`?action=save_lead_notes&id=${leadId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `notes=${encodeURIComponent(notes)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Notes saved successfully!');
                } else {
                    alert('Error saving notes: ' + data.error);
                }
            })
            .catch(error => {
                alert('Network error: ' + error.message);
            });
        }
        
        function runScraper() {
            window.open('scraper.php', '_blank');
        }
        
        function exportLeads() {
            window.open('export.php', '_blank');
        }
        
        function viewLogs() {
            window.open('logs.php', '_blank');
        }
    </script>
</body>
</html>
