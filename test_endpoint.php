<?php
/**
 * Test the leads endpoint directly
 */

require_once 'config/config.php';
require_once 'includes/Database.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance();
    $pdo = $db->getPDO();
    
    // Simple test query
    $stmt = $pdo->query("SELECT * FROM leads LIMIT 5");
    $leads = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'count' => count($leads),
        'leads' => $leads
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
