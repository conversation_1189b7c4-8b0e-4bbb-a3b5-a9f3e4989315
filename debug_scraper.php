<?php
/**
 * Debug scraper to see what's in the posts
 */

require_once 'config/config.php';
require_once 'includes/RedditAPISimple.php';
require_once 'includes/ContactExtractor.php';

if (isset($_GET['action']) && $_GET['action'] === 'debug') {
    header('Content-Type: application/json');
    
    try {
        $reddit = new RedditAPISimple();
        $contactExtractor = new ContactExtractor();
        
        // Get posts from Dubai subreddit
        $response = $reddit->getSubredditPosts('dubai', 'new', 5);
        $posts = $reddit->extractPostsData($response);
        
        $debug_info = [];
        
        foreach ($posts as $post) {
            $fullText = $post['title'] . ' ' . $post['content'];
            $contacts = $contactExtractor->extractContacts($fullText, $post['author']);
            
            $debug_info[] = [
                'title' => substr($post['title'], 0, 100),
                'content' => substr($post['content'], 0, 200),
                'author' => $post['author'],
                'has_contacts' => !empty($contacts),
                'contacts_found' => count($contacts),
                'contacts' => $contacts,
                'full_text_sample' => substr($fullText, 0, 300)
            ];
        }
        
        echo json_encode([
            'success' => true,
            'posts_checked' => count($posts),
            'debug_info' => $debug_info
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Scraper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Debug Scraper</h2>
        <p>Let's see what's actually in the Reddit posts</p>
        
        <button class="btn btn-primary" onclick="debugScraper()">
            <i class="fas fa-bug"></i> Debug Recent Posts
        </button>
        
        <div id="debug-results" class="mt-4"></div>
    </div>

    <script>
        function debugScraper() {
            document.getElementById('debug-results').innerHTML = '<div class="alert alert-info">Checking posts...</div>';
            
            fetch('?action=debug')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = `<h4>Checked ${data.posts_checked} posts:</h4>`;
                        
                        data.debug_info.forEach((post, index) => {
                            html += `
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <strong>Post ${index + 1}: ${post.title}</strong>
                                        <span class="badge ${post.has_contacts ? 'bg-success' : 'bg-secondary'} ms-2">
                                            ${post.contacts_found} contacts found
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>Author:</strong> ${post.author}</p>
                                        <p><strong>Content:</strong> ${post.content || 'No content'}</p>
                                        <p><strong>Sample text:</strong> ${post.full_text_sample}</p>
                                        ${post.contacts_found > 0 ? 
                                            `<div class="alert alert-success">
                                                <strong>Contacts found:</strong><br>
                                                ${post.contacts.map(c => `${c.type}: ${c.value}`).join('<br>')}
                                            </div>` : 
                                            '<div class="alert alert-warning">No contacts found in this post</div>'
                                        }
                                    </div>
                                </div>
                            `;
                        });
                        
                        document.getElementById('debug-results').innerHTML = html;
                    } else {
                        document.getElementById('debug-results').innerHTML = `
                            <div class="alert alert-danger">Error: ${data.error}</div>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('debug-results').innerHTML = `
                        <div class="alert alert-danger">Network error: ${error.message}</div>
                    `;
                });
        }
    </script>
</body>
</html>
