<?php
/**
 * Simple Reddit Scraper - Main Interface
 */

require_once 'config/config.php';
require_once 'includes/Database.php';
require_once 'includes/RedditAPISimple.php';
require_once 'includes/ContactExtractor.php';

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'scrape_single':
            try {
                $subreddit = $_GET['subreddit'] ?? 'dubai';
                $limit = min((int)($_GET['limit'] ?? 20), 100); // Max 100 posts
                
                $reddit = new RedditAPISimple();
                $db = Database::getInstance();
                $pdo = $db->getPDO();
                $contactExtractor = new ContactExtractor();
                
                // Get posts
                $response = $reddit->getSubredditPosts($subreddit, 'new', $limit);
                $posts = $reddit->extractPostsData($response);
                
                $leadsFound = 0;
                $postsProcessed = 0;
                
                foreach ($posts as $post) {
                    // Check for contact info in post
                    $contacts = $contactExtractor->extractContacts($post['title'] . ' ' . $post['content'], $post['author']);
                    
                    if (!empty($contacts)) {
                        foreach ($contacts as $contact) {
                            // Save to database
                            $stmt = $pdo->prepare("INSERT INTO leads (source_type, source_id, contact_type, contact_value, author, intent_score, context) VALUES (?, ?, ?, ?, ?, ?, ?)");
                            $stmt->execute([
                                'post',
                                $post['reddit_id'],
                                $contact['type'],
                                $contact['value'],
                                $contact['author'],
                                5, // Simple score
                                $contact['context'] // Save full context
                            ]);
                            $leadsFound++;
                        }
                    }
                    $postsProcessed++;
                }
                
                echo json_encode([
                    'success' => true,
                    'posts' => $postsProcessed,
                    'leads' => $leadsFound,
                    'message' => "Found $leadsFound leads from $postsProcessed posts in r/$subreddit"
                ]);
                
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;
            
        case 'run_scraper':
            try {
                // Get list of subreddits to scrape
                $subreddits = $_GET['subreddits'] ?? 'dubai,dubairealestate,UAE';
                $subredditList = explode(',', $subreddits);
                $postsPerSubreddit = min((int)($_GET['posts_per_subreddit'] ?? 30), 50); // Max 50 per subreddit

                // Get keywords for filtering
                $keywords = $_GET['keywords'] ?? '';
                $keywordList = [];
                if (!empty($keywords)) {
                    $keywordList = array_map('trim', explode(',', strtolower($keywords)));
                }

                // Get contact requirement setting
                $contactRequirement = $_GET['contact_requirement'] ?? 'required';
                
                $reddit = new RedditAPISimple();
                $db = Database::getInstance();
                $pdo = $db->getPDO();
                $contactExtractor = new ContactExtractor();
                
                $totalLeads = 0;
                $totalPosts = 0;
                $results = [];
                
                foreach ($subredditList as $subreddit) {
                    $subreddit = trim($subreddit);
                    if (empty($subreddit)) continue;
                    
                    try {
                        // Get posts from this subreddit
                        $response = $reddit->getSubredditPosts($subreddit, 'new', $postsPerSubreddit);
                        $posts = $reddit->extractPostsData($response);
                        
                        $subredditLeads = 0;
                        
                        foreach ($posts as $post) {
                            $fullText = $post['title'] . ' ' . $post['content'];

                            // Apply keyword filtering if keywords are specified
                            $passesKeywordFilter = true;
                            if (!empty($keywordList)) {
                                $passesKeywordFilter = false;
                                $lowerText = strtolower($fullText);
                                foreach ($keywordList as $keyword) {
                                    if (strpos($lowerText, $keyword) !== false) {
                                        $passesKeywordFilter = true;
                                        break;
                                    }
                                }
                            }

                            // Only process posts that pass keyword filter
                            if ($passesKeywordFilter) {
                                $contacts = $contactExtractor->extractContacts($fullText, $post['author']);

                                if ($contactRequirement === 'optional') {
                                    // Save all matching posts, even without contact info
                                    if (!empty($contacts)) {
                                        // Has contact info - save each contact
                                        foreach ($contacts as $contact) {
                                            $stmt = $pdo->prepare("INSERT INTO leads (source_type, source_id, contact_type, contact_value, author, intent_score, context) VALUES (?, ?, ?, ?, ?, ?, ?)");
                                            $stmt->execute([
                                                'post',
                                                $post['reddit_id'],
                                                $contact['type'],
                                                $contact['value'],
                                                $contact['author'],
                                                5,
                                                $contact['context']
                                            ]);
                                            $subredditLeads++;
                                        }
                                    } else {
                                        // No contact info but matches keywords - save as potential lead
                                        $stmt = $pdo->prepare("INSERT INTO leads (source_type, source_id, contact_type, contact_value, author, intent_score, context) VALUES (?, ?, ?, ?, ?, ?, ?)");
                                        $stmt->execute([
                                            'post',
                                            $post['reddit_id'],
                                            'potential',
                                            'No contact info - Reddit user: ' . $post['author'],
                                            $post['author'],
                                            3, // Lower score for no contact
                                            $fullText
                                        ]);
                                        $subredditLeads++;
                                    }
                                } else {
                                    // Only save posts with contact info (original behavior)
                                    if (!empty($contacts)) {
                                        foreach ($contacts as $contact) {
                                            $stmt = $pdo->prepare("INSERT INTO leads (source_type, source_id, contact_type, contact_value, author, intent_score, context) VALUES (?, ?, ?, ?, ?, ?, ?)");
                                            $stmt->execute([
                                                'post',
                                                $post['reddit_id'],
                                                $contact['type'],
                                                $contact['value'],
                                                $contact['author'],
                                                5,
                                                $contact['context']
                                            ]);
                                            $subredditLeads++;
                                        }
                                    }
                                }
                            }
                        }
                        
                        $results[$subreddit] = [
                            'posts' => count($posts),
                            'leads' => $subredditLeads
                        ];
                        
                        $totalPosts += count($posts);
                        $totalLeads += $subredditLeads;
                        
                        // Small delay between subreddits
                        sleep(1);
                        
                    } catch (Exception $e) {
                        $results[$subreddit] = ['error' => $e->getMessage()];
                    }
                }
                
                echo json_encode([
                    'success' => true,
                    'total_posts' => $totalPosts,
                    'total_leads' => $totalLeads,
                    'results' => $results,
                    'message' => "Scraping completed! Found $totalLeads leads from $totalPosts posts across " . count($subredditList) . " subreddits"
                ]);
                
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'error' => $e->getMessage()]);
            }
            exit;

        case 'get_stats':
            try {
                $db = Database::getInstance();
                $pdo = $db->getPDO();

                $stats = [];
                $stats['total_posts'] = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
                $stats['total_comments'] = $pdo->query("SELECT COUNT(*) FROM comments")->fetchColumn();
                $stats['total_leads'] = $pdo->query("SELECT COUNT(*) FROM leads")->fetchColumn();

                echo json_encode($stats);

            } catch (Exception $e) {
                echo json_encode(['error' => $e->getMessage()]);
            }
            exit;
    }
}

// Get current stats for display
$db = Database::getInstance();
$pdo = $db->getPDO();

$totalLeads = $pdo->query("SELECT COUNT(*) FROM leads")->fetchColumn();
$totalPosts = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
$totalComments = $pdo->query("SELECT COUNT(*) FROM comments")->fetchColumn();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit Scraper Control Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-robot"></i> Reddit Scraper Control Panel</h2>
                    <a href="index.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
                
                <!-- Current Stats -->
                <div class="row mb-4">
                    <div class="col-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?= $totalLeads ?></h3>
                                <p>Total Leads</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?= $totalPosts ?></h3>
                                <p>Posts Scraped</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info"><?= $totalComments ?></h3>
                                <p>Comments</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Scraper Controls -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Scraper Controls</h5>
                    </div>
                    <div class="card-body">
                        <!-- Custom Subreddit List -->
                        <div class="mb-3">
                            <label class="form-label">Subreddits to scrape (comma-separated):</label>
                            <input type="text" class="form-control" id="subreddit-list"
                                   value="dubai,dubairealestate,UAE,DubaiPetrolHeads,dubai_expats"
                                   placeholder="dubai,dubairealestate,UAE">
                            <small class="text-muted">Add any subreddit names separated by commas</small>
                        </div>

                        <!-- Keyword Filtering -->
                        <div class="mb-3">
                            <label class="form-label">Keywords to look for (optional):</label>
                            <input type="text" class="form-control" id="keyword-filter"
                                   value="looking to buy,want to buy,need apartment,need villa,relocating,moving to dubai,first time buyer,budget,searching for,looking for apartment"
                                   placeholder="looking to buy,need apartment,relocating">
                            <small class="text-muted">Only scrape posts containing these keywords (leave empty to scrape all posts)</small>
                        </div>

                        <!-- Contact Requirement -->
                        <div class="mb-3">
                            <label class="form-label">Contact Information Requirement:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="contact-requirement" id="contact-required" value="required" checked>
                                <label class="form-check-label" for="contact-required">
                                    <strong>Only posts with contact info</strong> (phone/email) - Traditional leads
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="contact-requirement" id="contact-optional" value="optional">
                                <label class="form-check-label" for="contact-optional">
                                    <strong>All matching posts</strong> (with or without contact) - Potential leads to reach out to
                                </label>
                            </div>
                            <small class="text-muted">Choose whether to require contact information or save all matching posts</small>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label">Posts per subreddit:</label>
                                <select class="form-select" id="posts-per-subreddit">
                                    <option value="20">20 posts</option>
                                    <option value="30" selected>30 posts</option>
                                    <option value="40">40 posts</option>
                                    <option value="50">50 posts</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-success w-100" onclick="runFullScraper()">
                                    <i class="fas fa-rocket"></i> Run Full Scraper
                                </button>
                            </div>
                        </div>
                        
                        <!-- Quick Add Buttons -->
                        <div class="mb-3">
                            <label class="form-label">Quick add subreddits:</label><br>
                            <button class="btn btn-outline-primary btn-sm me-1 mb-1" onclick="addSubreddit('realestate')">r/realestate</button>
                            <button class="btn btn-outline-primary btn-sm me-1 mb-1" onclick="addSubreddit('dubai_jobs')">r/dubai_jobs</button>
                            <button class="btn btn-outline-primary btn-sm me-1 mb-1" onclick="addSubreddit('abudhabi')">r/abudhabi</button>
                            <button class="btn btn-outline-primary btn-sm me-1 mb-1" onclick="addSubreddit('sharjah')">r/sharjah</button>
                            <button class="btn btn-outline-primary btn-sm me-1 mb-1" onclick="addSubreddit('emirates')">r/emirates</button>
                            <button class="btn btn-outline-secondary btn-sm mb-1" onclick="clearSubreddits()">Clear All</button>
                        </div>
                        
                        <!-- Single Subreddit Quick Test -->
                        <div class="mb-3">
                            <label class="form-label">Quick single test:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="single-subreddit" 
                                       value="dubai" placeholder="subreddit name">
                                <select class="form-select" id="single-limit" style="max-width: 100px;">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="30">30</option>
                                    <option value="50">50</option>
                                </select>
                                <button class="btn btn-primary" onclick="runSingleSubreddit()">
                                    <i class="fas fa-search"></i> Test
                                </button>
                            </div>
                        </div>
                        
                        <div id="scraper-status" class="mt-3"></div>
                    </div>
                </div>
                
                <!-- Navigation -->
                <div class="mt-4">
                    <a href="leads.php" class="btn btn-info">
                        <i class="fas fa-users"></i> View Leads
                    </a>
                    <a href="export.php" class="btn btn-success">
                        <i class="fas fa-download"></i> Export Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load saved settings on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedSettings();
        });

        function loadSavedSettings() {
            const savedSubreddits = localStorage.getItem('scraper_subreddits');
            const savedPostsPerSubreddit = localStorage.getItem('scraper_posts_per_subreddit');
            const savedKeywords = localStorage.getItem('scraper_keywords');
            const savedContactRequirement = localStorage.getItem('scraper_contact_requirement');

            if (savedSubreddits) {
                document.getElementById('subreddit-list').value = savedSubreddits;
            }
            if (savedPostsPerSubreddit) {
                document.getElementById('posts-per-subreddit').value = savedPostsPerSubreddit;
            }
            if (savedKeywords) {
                document.getElementById('keyword-filter').value = savedKeywords;
            }
            if (savedContactRequirement) {
                const radioButton = document.getElementById(savedContactRequirement === 'required' ? 'contact-required' : 'contact-optional');
                if (radioButton) {
                    radioButton.checked = true;
                }
            }
        }

        function saveSettings() {
            const subreddits = document.getElementById('subreddit-list').value;
            const postsPerSubreddit = document.getElementById('posts-per-subreddit').value;
            const keywords = document.getElementById('keyword-filter').value;
            const contactRequirement = document.querySelector('input[name="contact-requirement"]:checked').value;

            localStorage.setItem('scraper_subreddits', subreddits);
            localStorage.setItem('scraper_posts_per_subreddit', postsPerSubreddit);
            localStorage.setItem('scraper_keywords', keywords);
            localStorage.setItem('scraper_contact_requirement', contactRequirement);
        }

        function runFullScraper() {
            // Save settings before running
            saveSettings();
            const subreddits = document.getElementById('subreddit-list').value;
            const postsPerSubreddit = document.getElementById('posts-per-subreddit').value;
            const keywords = document.getElementById('keyword-filter').value;
            const contactRequirement = document.querySelector('input[name="contact-requirement"]:checked').value;

            document.getElementById('scraper-status').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i> Starting full scraper for: ${subreddits}
                    ${keywords ? '<br><small>Filtering for keywords: ' + keywords + '</small>' : ''}
                    <br><small>Contact requirement: ${contactRequirement === 'required' ? 'Only posts with contact info' : 'All matching posts'}</small>
                </div>
            `;

            fetch(`?action=run_scraper&subreddits=${encodeURIComponent(subreddits)}&posts_per_subreddit=${postsPerSubreddit}&keywords=${encodeURIComponent(keywords)}&contact_requirement=${contactRequirement}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let detailsHtml = `
                            <div class="alert alert-success">
                                <h6>✅ Scraping Complete!</h6>
                                <p>${data.message}</p>
                                <div class="mt-2"><strong>Results by subreddit:</strong>
                                <ul class="mb-0">
                        `;
                        
                        for (const [subreddit, result] of Object.entries(data.results)) {
                            if (result.error) {
                                detailsHtml += `<li>r/${subreddit}: <span class="text-danger">Error - ${result.error}</span></li>`;
                            } else {
                                detailsHtml += `<li>r/${subreddit}: ${result.posts} posts, <strong>${result.leads} leads</strong></li>`;
                            }
                        }
                        detailsHtml += '</ul></div></div>';
                        
                        document.getElementById('scraper-status').innerHTML = detailsHtml;

                        // Update stats without full page refresh
                        updateStatsDisplay();
                    } else {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-danger">
                                <h6>❌ Error</h6>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('scraper-status').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ Network Error</h6>
                            <p>${error.message}</p>
                        </div>
                    `;
                });
        }
        
        function runSingleSubreddit() {
            const subreddit = document.getElementById('single-subreddit').value;
            const limit = document.getElementById('single-limit').value;
            
            document.getElementById('scraper-status').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i> Testing r/${subreddit} (${limit} posts)...
                </div>
            `;
            
            fetch(`?action=scrape_single&subreddit=${subreddit}&limit=${limit}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-success">
                                <h6>✅ Test Complete!</h6>
                                <p>${data.message}</p>
                            </div>
                        `;
                        // Update stats without full page refresh
                        updateStatsDisplay();
                    } else {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-danger">
                                <h6>❌ Error</h6>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('scraper-status').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ Network Error</h6>
                            <p>${error.message}</p>
                        </div>
                    `;
                });
        }
        
        function addSubreddit(subreddit) {
            const input = document.getElementById('subreddit-list');
            const current = input.value.trim();

            if (current === '') {
                input.value = subreddit;
            } else {
                const subreddits = current.split(',').map(s => s.trim());
                if (!subreddits.includes(subreddit)) {
                    input.value = current + ',' + subreddit;
                }
            }
            saveSettings(); // Save after adding
        }

        function clearSubreddits() {
            document.getElementById('subreddit-list').value = '';
            saveSettings(); // Save after clearing
        }

        function updateStatsDisplay() {
            // Fetch updated stats and update the display
            fetch('?action=get_stats')
                .then(response => response.json())
                .then(data => {
                    if (!data.error) {
                        // Update the stats cards
                        const statsCards = document.querySelectorAll('.card-body h3');
                        if (statsCards[0]) statsCards[0].textContent = data.total_leads || 0;
                        if (statsCards[1]) statsCards[1].textContent = data.total_posts || 0;
                        if (statsCards[2]) statsCards[2].textContent = data.total_comments || 0;
                    }
                })
                .catch(error => {
                    console.log('Error updating stats:', error);
                });
        }

        // Auto-save settings when user types
        document.addEventListener('DOMContentLoaded', function() {
            const subredditInput = document.getElementById('subreddit-list');
            const postsSelect = document.getElementById('posts-per-subreddit');

            if (subredditInput) {
                subredditInput.addEventListener('input', saveSettings);
            }
            if (postsSelect) {
                postsSelect.addEventListener('change', saveSettings);
            }
        });
    </script>
</body>
</html>
