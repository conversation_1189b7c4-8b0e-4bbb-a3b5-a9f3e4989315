<?php
/**
 * Reddit Scraper Execution Script
 */

require_once 'config/config.php';
require_once 'includes/RedditScraper.php';

// Set execution time limit
set_time_limit(0);

// Handle AJAX requests
if (isset($_GET['action'])) {
    header('Content-Type: application/json');
    
    switch ($_GET['action']) {
        case 'run_scraper':
            try {
                $scraper = new RedditScraper();
                $results = $scraper->scrapeAll();
                
                echo json_encode([
                    'success' => true,
                    'results' => $results,
                    'message' => "Scraping completed. Found {$results['total_leads']} new leads."
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'error' => $e->getMessage()
                ]);
            }
            exit;
            
        case 'run_single_subreddit':
            $subreddit = $_GET['subreddit'] ?? '';
            if (empty($subreddit)) {
                echo json_encode(['success' => false, 'error' => 'Subreddit not specified']);
                exit;
            }
            
            try {
                $scraper = new RedditScraper();
                $results = $scraper->scrapeSubreddit($subreddit);
                
                echo json_encode([
                    'success' => true,
                    'results' => $results,
                    'message' => "Scraped r/{$subreddit}. Found {$results['leads']} new leads."
                ]);
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'error' => $e->getMessage()
                ]);
            }
            exit;
            
        case 'get_scraping_status':
            $db = Database::getInstance();
            $pdo = $db->getPDO();
            
            // Get recent scraping logs
            $stmt = $pdo->query("
                SELECT * FROM scraping_logs 
                ORDER BY created_at DESC 
                LIMIT 10
            ");
            $logs = $stmt->fetchAll();
            
            // Get last scraping times for each subreddit
            $stmt = $pdo->query("
                SELECT name, last_scraped, total_posts_scraped 
                FROM subreddits 
                ORDER BY last_scraped DESC
            ");
            $subreddits = $stmt->fetchAll();
            
            echo json_encode([
                'logs' => $logs,
                'subreddits' => $subreddits
            ]);
            exit;
    }
}

// Check if Reddit API credentials are configured
$credentialsConfigured = !empty(REDDIT_CLIENT_ID) && !empty(REDDIT_CLIENT_SECRET);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reddit Scraper Control Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-success { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .log-entry {
            border-left: 4px solid #007bff;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
        }
        .log-entry.error { border-left-color: #dc3545; }
        .log-entry.warning { border-left-color: #ffc107; }
        .log-entry.success { border-left-color: #28a745; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-robot"></i> Reddit Scraper Control Panel</h2>
                    <a href="index.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
                
                <?php if (!$credentialsConfigured): ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> Configuration Required</h5>
                    <p>Reddit API credentials are not configured. Please update the following in <code>config/config.php</code>:</p>
                    <ul>
                        <li><strong>REDDIT_CLIENT_ID</strong> - Your Reddit app client ID</li>
                        <li><strong>REDDIT_CLIENT_SECRET</strong> - Your Reddit app client secret</li>
                        <li><strong>REDDIT_USER_AGENT</strong> - A unique user agent string</li>
                    </ul>
                    <p><a href="https://www.reddit.com/prefs/apps" target="_blank">Create a Reddit app here</a> to get these credentials.</p>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Scraper Controls -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-play"></i> Scraper Controls</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <button class="btn btn-success w-100 mb-2" onclick="runFullScraper()" <?= !$credentialsConfigured ? 'disabled' : '' ?>>
                                        <i class="fas fa-play"></i> Run Full Scraper (All Subreddits)
                                    </button>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Scrape Single Subreddit:</label>
                                    <div class="input-group">
                                        <select class="form-select" id="subreddit-select">
                                            <?php foreach (TARGET_SUBREDDITS as $subreddit): ?>
                                                <option value="<?= htmlspecialchars($subreddit) ?>">r/<?= htmlspecialchars($subreddit) ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                        <button class="btn btn-primary" onclick="runSingleSubreddit()" <?= !$credentialsConfigured ? 'disabled' : '' ?>>
                                            <i class="fas fa-play"></i> Scrape
                                        </button>
                                    </div>
                                </div>
                                
                                <div id="scraper-status" class="mt-3"></div>
                            </div>
                        </div>
                        
                        <!-- Subreddit Status -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5><i class="fas fa-list"></i> Subreddit Status</h5>
                            </div>
                            <div class="card-body">
                                <div id="subreddit-status">
                                    <div class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> Loading...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Logs -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-list-alt"></i> Recent Scraping Logs</h5>
                            </div>
                            <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                                <div id="scraping-logs">
                                    <div class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> Loading logs...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Configuration Info -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-cog"></i> Current Configuration</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Target Subreddits:</h6>
                                        <ul>
                                            <?php foreach (TARGET_SUBREDDITS as $subreddit): ?>
                                                <li>r/<?= htmlspecialchars($subreddit) ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Settings:</h6>
                                        <ul>
                                            <li>Posts per request: <?= POSTS_PER_REQUEST ?></li>
                                            <li>Max posts per subreddit: <?= MAX_POSTS_PER_SUBREDDIT ?></li>
                                            <li>Scraping interval: <?= SCRAPING_INTERVAL_MINUTES ?> minutes</li>
                                            <li>Min intent score: <?= MIN_INTENT_SCORE ?></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load status on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadScrapingStatus();
            
            // Auto-refresh every 30 seconds
            setInterval(loadScrapingStatus, 30000);
        });
        
        function runFullScraper() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running...';
            
            document.getElementById('scraper-status').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i> Running full scraper on all subreddits...
                </div>
            `;
            
            fetch('?action=run_scraper')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-success">
                                <h6>Scraping Completed Successfully!</h6>
                                <p>${data.message}</p>
                                <small>
                                    Posts: ${data.results.total_posts} | 
                                    Comments: ${data.results.total_comments} | 
                                    Leads: ${data.results.total_leads}
                                </small>
                            </div>
                        `;
                    } else {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-danger">
                                <h6>Scraping Failed</h6>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                    
                    loadScrapingStatus(); // Refresh logs
                })
                .catch(error => {
                    document.getElementById('scraper-status').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>Error</h6>
                            <p>Failed to run scraper: ${error.message}</p>
                        </div>
                    `;
                })
                .finally(() => {
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
        }
        
        function runSingleSubreddit() {
            const subreddit = document.getElementById('subreddit-select').value;
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            document.getElementById('scraper-status').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-spinner fa-spin"></i> Scraping r/${subreddit}...
                </div>
            `;
            
            fetch(`?action=run_single_subreddit&subreddit=${subreddit}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-success">
                                <h6>Scraping Completed!</h6>
                                <p>${data.message}</p>
                                <small>
                                    Posts: ${data.results.posts} | 
                                    Comments: ${data.results.comments} | 
                                    Leads: ${data.results.leads}
                                </small>
                            </div>
                        `;
                    } else {
                        document.getElementById('scraper-status').innerHTML = `
                            <div class="alert alert-danger">
                                <h6>Scraping Failed</h6>
                                <p>${data.error}</p>
                            </div>
                        `;
                    }
                    
                    loadScrapingStatus(); // Refresh logs
                })
                .catch(error => {
                    document.getElementById('scraper-status').innerHTML = `
                        <div class="alert alert-danger">
                            <h6>Error</h6>
                            <p>Failed to scrape subreddit: ${error.message}</p>
                        </div>
                    `;
                })
                .finally(() => {
                    button.disabled = false;
                    button.innerHTML = originalText;
                });
        }
        
        function loadScrapingStatus() {
            fetch('?action=get_scraping_status')
                .then(response => response.json())
                .then(data => {
                    displayLogs(data.logs);
                    displaySubredditStatus(data.subreddits);
                })
                .catch(error => {
                    console.error('Error loading scraping status:', error);
                });
        }
        
        function displayLogs(logs) {
            if (logs.length === 0) {
                document.getElementById('scraping-logs').innerHTML = '<div class="text-muted">No logs available</div>';
                return;
            }
            
            const logsHtml = logs.map(log => {
                const statusClass = log.status === 'success' ? 'success' : 
                                  log.status === 'error' ? 'error' : 'warning';
                
                return `
                    <div class="log-entry ${statusClass}">
                        <div class="d-flex justify-content-between">
                            <strong>${log.subreddit || 'System'} - ${log.action}</strong>
                            <small>${formatDate(log.created_at)}</small>
                        </div>
                        <div class="small">${log.message}</div>
                        ${log.posts_found || log.comments_found || log.leads_found ? 
                            `<div class="small text-muted">
                                Posts: ${log.posts_found || 0} | 
                                Comments: ${log.comments_found || 0} | 
                                Leads: ${log.leads_found || 0}
                            </div>` : ''
                        }
                    </div>
                `;
            }).join('');
            
            document.getElementById('scraping-logs').innerHTML = logsHtml;
        }
        
        function displaySubredditStatus(subreddits) {
            if (subreddits.length === 0) {
                document.getElementById('subreddit-status').innerHTML = '<div class="text-muted">No subreddit data available</div>';
                return;
            }
            
            const statusHtml = subreddits.map(sub => {
                const lastScraped = sub.last_scraped ? 
                    formatDate(sub.last_scraped) : 
                    '<span class="text-muted">Never</span>';
                
                return `
                    <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                        <div>
                            <strong>r/${sub.name}</strong><br>
                            <small class="text-muted">Posts scraped: ${sub.total_posts_scraped || 0}</small>
                        </div>
                        <div class="text-end">
                            <small>Last scraped:<br>${lastScraped}</small>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('subreddit-status').innerHTML = statusHtml;
        }
        
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString() + ' ' + 
                   new Date(dateString).toLocaleTimeString();
        }
    </script>
</body>
</html>
