<?php
/**
 * Setup and Configuration Script for Reddit Dubai Real Estate Scraper
 */

require_once 'config/config.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'save_config') {
        $clientId = $_POST['client_id'] ?? '';
        $clientSecret = $_POST['client_secret'] ?? '';
        $userAgent = $_POST['user_agent'] ?? '';
        
        // Update config file
        $configPath = 'config/config.php';
        $configContent = file_get_contents($configPath);
        
        $configContent = preg_replace(
            "/define\('REDDIT_CLIENT_ID', '.*?'\);/",
            "define('REDDIT_CLIENT_ID', '$clientId');",
            $configContent
        );
        
        $configContent = preg_replace(
            "/define\('REDDIT_CLIENT_SECRET', '.*?'\);/",
            "define('REDDIT_CLIENT_SECRET', '$clientSecret');",
            $configContent
        );
        
        $configContent = preg_replace(
            "/define\('REDDIT_USER_AGENT', '.*?'\);/",
            "define('REDDIT_USER_AGENT', '$userAgent');",
            $configContent
        );
        
        if (file_put_contents($configPath, $configContent)) {
            $success = "Configuration saved successfully!";
        } else {
            $error = "Failed to save configuration. Please check file permissions.";
        }
    }
    
    if ($action === 'test_connection') {
        try {
            require_once 'includes/RedditAPI.php';
            $reddit = new RedditAPI();
            
            // Test by getting a simple subreddit listing
            $response = $reddit->getSubredditPosts('test', 'hot', 1);
            
            if ($response && isset($response['data'])) {
                $success = "Reddit API connection successful!";
            } else {
                $error = "Reddit API connection failed - invalid response";
            }
        } catch (Exception $e) {
            $error = "Reddit API connection failed: " . $e->getMessage();
        }
    }
    
    if ($action === 'create_database') {
        try {
            $db = Database::getInstance();
            $success = "Database created successfully!";
        } catch (Exception $e) {
            $error = "Database creation failed: " . $e->getMessage();
        }
    }
}

// Check current configuration status
$configStatus = [
    'client_id' => !empty(REDDIT_CLIENT_ID),
    'client_secret' => !empty(REDDIT_CLIENT_SECRET),
    'user_agent' => !empty(REDDIT_USER_AGENT),
    'database' => file_exists(DB_PATH)
];

$allConfigured = array_reduce($configStatus, function($carry, $item) {
    return $carry && $item;
}, true);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - Reddit Dubai Real Estate Scraper</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .setup-step {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .setup-step.completed {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .setup-step.pending {
            border-color: #ffc107;
            background-color: #fffdf5;
        }
        .status-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }
        .status-completed { color: #28a745; }
        .status-pending { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="text-center mb-4">
                    <h1><i class="fas fa-cog"></i> Reddit Dubai Real Estate Scraper Setup</h1>
                    <p class="text-muted">Configure your application to start scraping Reddit for real estate leads</p>
                </div>
                
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>
                
                <!-- Step 1: Reddit API Configuration -->
                <div class="setup-step <?= $configStatus['client_id'] && $configStatus['client_secret'] && $configStatus['user_agent'] ? 'completed' : 'pending' ?>">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-key status-icon <?= $configStatus['client_id'] && $configStatus['client_secret'] && $configStatus['user_agent'] ? 'status-completed' : 'status-pending' ?>"></i>
                        <h4 class="mb-0">Step 1: Reddit API Configuration</h4>
                    </div>
                    
                    <p>Configure your Reddit API credentials to access Reddit data.</p>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <form method="POST">
                                <input type="hidden" name="action" value="save_config">
                                
                                <div class="mb-3">
                                    <label class="form-label">Client ID</label>
                                    <input type="text" name="client_id" class="form-control" 
                                           value="<?= htmlspecialchars(REDDIT_CLIENT_ID) ?>" 
                                           placeholder="Your Reddit app client ID">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Client Secret</label>
                                    <input type="password" name="client_secret" class="form-control" 
                                           value="<?= htmlspecialchars(REDDIT_CLIENT_SECRET) ?>" 
                                           placeholder="Your Reddit app client secret">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">User Agent</label>
                                    <input type="text" name="user_agent" class="form-control" 
                                           value="<?= htmlspecialchars(REDDIT_USER_AGENT) ?>" 
                                           placeholder="YourAppName/1.0 by YourUsername">
                                </div>
                                
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Configuration
                                </button>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> How to get Reddit API credentials</h6>
                                </div>
                                <div class="card-body">
                                    <ol class="small">
                                        <li>Go to <a href="https://www.reddit.com/prefs/apps" target="_blank">Reddit Apps</a></li>
                                        <li>Click "Create App" or "Create Another App"</li>
                                        <li>Choose "script" as the app type</li>
                                        <li>Fill in the form and submit</li>
                                        <li>Copy the Client ID and Secret</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Database Setup -->
                <div class="setup-step <?= $configStatus['database'] ? 'completed' : 'pending' ?>">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-database status-icon <?= $configStatus['database'] ? 'status-completed' : 'status-pending' ?>"></i>
                        <h4 class="mb-0">Step 2: Database Setup</h4>
                    </div>
                    
                    <p>Initialize the SQLite database and create required tables.</p>
                    
                    <?php if ($configStatus['database']): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check"></i> Database is already set up at: <code><?= DB_PATH ?></code>
                        </div>
                    <?php else: ?>
                        <form method="POST">
                            <input type="hidden" name="action" value="create_database">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Database
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
                
                <!-- Step 3: Test Connection -->
                <div class="setup-step">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-wifi status-icon status-pending"></i>
                        <h4 class="mb-0">Step 3: Test Reddit API Connection</h4>
                    </div>
                    
                    <p>Test your Reddit API configuration to ensure it's working correctly.</p>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="test_connection">
                        <button type="submit" class="btn btn-info" 
                                <?= !($configStatus['client_id'] && $configStatus['client_secret']) ? 'disabled' : '' ?>>
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                    </form>
                </div>
                
                <!-- Step 4: Ready to Use -->
                <?php if ($allConfigured): ?>
                <div class="setup-step completed">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-check-circle status-icon status-completed"></i>
                        <h4 class="mb-0">Setup Complete!</h4>
                    </div>
                    
                    <p>Your Reddit Dubai Real Estate Scraper is ready to use.</p>
                    
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="index.php" class="btn btn-success">
                            <i class="fas fa-home"></i> Go to Dashboard
                        </a>
                        <a href="scraper.php" class="btn btn-primary">
                            <i class="fas fa-play"></i> Start Scraping
                        </a>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Configuration Summary -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Configuration Summary</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Target Subreddits:</h6>
                                <ul class="small">
                                    <?php foreach (TARGET_SUBREDDITS as $subreddit): ?>
                                        <li>r/<?= htmlspecialchars($subreddit) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Configuration Status:</h6>
                                <ul class="list-unstyled small">
                                    <li>
                                        <i class="fas fa-<?= $configStatus['client_id'] ? 'check text-success' : 'times text-danger' ?>"></i>
                                        Reddit Client ID
                                    </li>
                                    <li>
                                        <i class="fas fa-<?= $configStatus['client_secret'] ? 'check text-success' : 'times text-danger' ?>"></i>
                                        Reddit Client Secret
                                    </li>
                                    <li>
                                        <i class="fas fa-<?= $configStatus['user_agent'] ? 'check text-success' : 'times text-danger' ?>"></i>
                                        User Agent
                                    </li>
                                    <li>
                                        <i class="fas fa-<?= $configStatus['database'] ? 'check text-success' : 'times text-danger' ?>"></i>
                                        Database
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Cron Setup Instructions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-clock"></i> Automated Scraping Setup (Optional)</h5>
                    </div>
                    <div class="card-body">
                        <p>To run the scraper automatically, add this to your crontab:</p>
                        <div class="bg-light p-3 rounded">
                            <code>0 * * * * /usr/bin/php <?= __DIR__ ?>/cron_scraper.php</code>
                        </div>
                        <small class="text-muted">This will run the scraper every hour. Adjust the schedule as needed.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
