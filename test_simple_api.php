<?php
/**
 * Test Simple Reddit API
 */

require_once 'config/config.php';
require_once 'includes/RedditAPISimple.php';

try {
    echo "Testing Simple Reddit API...\n";
    
    $reddit = new RedditAPISimple();
    
    // Test by getting a few posts from r/test
    echo "Fetching posts from r/test...\n";
    $response = $reddit->getSubredditPosts('test', 'hot', 5);
    
    if ($response && isset($response['data']['children'])) {
        echo "✅ Reddit API connection successful!\n";
        echo "Found " . count($response['data']['children']) . " posts from r/test\n";
        
        // Test extracting posts data
        $posts = $reddit->extractPostsData($response);
        echo "✅ Post extraction working - extracted " . count($posts) . " posts\n";
        
        if (!empty($posts)) {
            echo "Sample post: " . substr($posts[0]['title'], 0, 50) . "...\n";
            echo "Author: " . $posts[0]['author'] . "\n";
        }
        
        // Test getting posts from Dubai subreddit
        echo "\nTesting Dubai subreddit...\n";
        $dubaiResponse = $reddit->getSubredditPosts('dubai', 'new', 3);
        
        if ($dubaiResponse && isset($dubaiResponse['data']['children'])) {
            $dubaiPosts = $reddit->extractPostsData($dubaiResponse);
            echo "✅ Found " . count($dubaiPosts) . " posts from r/dubai\n";
            
            if (!empty($dubaiPosts)) {
                echo "Sample Dubai post: " . substr($dubaiPosts[0]['title'], 0, 60) . "...\n";
            }
        }
        
    } else {
        echo "❌ Reddit API connection failed - invalid response\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ Reddit API connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 Simple Reddit API is working! Ready to scrape.\n";
?>
