<?php
/**
 * Simple Reddit API Client using public JSON endpoints
 * This approach doesn't require OAuth authentication
 */

class RedditAPISimple {
    private $userAgent;
    private $baseUrl = 'https://www.reddit.com';
    
    public function __construct() {
        $this->userAgent = REDDIT_USER_AGENT;
    }
    
    /**
     * Make HTTP request to Reddit
     */
    private function makeRequest($url, $params = []) {
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'User-Agent: Mozilla/5.0 (compatible; ' . $this->userAgent . ')',
                'Accept: application/json',
                'Accept-Language: en-US,en;q=0.9',
                'Accept-Encoding: gzip, deflate',
                'DNT: 1',
                'Connection: keep-alive',
                'Upgrade-Insecure-Requests: 1'
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_ENCODING => 'gzip',
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; ' . $this->userAgent . ')'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);

        if ($curlError) {
            throw new Exception('cURL error: ' . $curlError);
        }

        if ($httpCode !== 200) {
            throw new Exception('Reddit request failed. HTTP Code: ' . $httpCode . ' URL: ' . $url . ' Response: ' . substr($response, 0, 200));
        }

        $data = json_decode($response, true);
        if (!$data) {
            throw new Exception('Invalid JSON response from Reddit: ' . substr($response, 0, 200));
        }

        return $data;
    }
    
    /**
     * Get posts from a subreddit
     */
    public function getSubredditPosts($subreddit, $sort = 'new', $limit = 25, $after = null) {
        $params = [
            'limit' => min($limit, 100),
            'raw_json' => 1
        ];
        
        if ($after) {
            $params['after'] = $after;
        }
        
        $url = "{$this->baseUrl}/r/{$subreddit}/{$sort}.json";
        return $this->makeRequest($url, $params);
    }
    
    /**
     * Get comments for a specific post
     */
    public function getPostComments($subreddit, $postId, $limit = 100) {
        $params = [
            'limit' => min($limit, 500),
            'depth' => MAX_COMMENT_DEPTH,
            'raw_json' => 1
        ];
        
        $url = "{$this->baseUrl}/r/{$subreddit}/comments/{$postId}.json";
        return $this->makeRequest($url, $params);
    }
    
    /**
     * Search posts in a subreddit
     */
    public function searchSubreddit($subreddit, $query, $sort = 'new', $limit = 25) {
        $params = [
            'q' => $query,
            'restrict_sr' => 'true',
            'sort' => $sort,
            'limit' => min($limit, 100),
            'raw_json' => 1
        ];
        
        $url = "{$this->baseUrl}/r/{$subreddit}/search.json";
        return $this->makeRequest($url, $params);
    }
    
    /**
     * Rate limiting helper
     */
    public function respectRateLimit() {
        // Be more conservative with public API
        sleep(2);
    }
    
    /**
     * Extract posts data from Reddit API response
     */
    public function extractPostsData($response) {
        $posts = [];
        
        if (!isset($response['data']['children'])) {
            return $posts;
        }
        
        foreach ($response['data']['children'] as $child) {
            if ($child['kind'] !== 't3') continue; // t3 = Link/Post
            
            $post = $child['data'];
            $posts[] = [
                'reddit_id' => $post['id'],
                'title' => $post['title'] ?? '',
                'content' => $post['selftext'] ?? '',
                'author' => $post['author'] ?? '[deleted]',
                'url' => $post['url'] ?? '',
                'score' => $post['score'] ?? 0,
                'num_comments' => $post['num_comments'] ?? 0,
                'created_utc' => $post['created_utc'] ?? time(),
                'subreddit' => $post['subreddit'] ?? '',
                'permalink' => $post['permalink'] ?? '',
                'is_self' => $post['is_self'] ?? false
            ];
        }
        
        return $posts;
    }
    
    /**
     * Extract comments data from Reddit API response
     */
    public function extractCommentsData($response, $postId = null) {
        $comments = [];
        
        if (!is_array($response) || count($response) < 2) {
            return $comments;
        }
        
        // Comments are in the second element of the response array
        $commentsData = $response[1];
        
        if (!isset($commentsData['data']['children'])) {
            return $comments;
        }
        
        $this->extractCommentsRecursive($commentsData['data']['children'], $comments, $postId);
        
        return $comments;
    }
    
    /**
     * Recursively extract comments and replies
     */
    private function extractCommentsRecursive($children, &$comments, $postId = null, $parentId = null) {
        foreach ($children as $child) {
            if ($child['kind'] !== 't1') continue; // t1 = Comment
            
            $comment = $child['data'];
            
            // Skip deleted comments
            if ($comment['author'] === '[deleted]' || empty($comment['body'])) {
                continue;
            }
            
            $commentData = [
                'reddit_id' => $comment['id'],
                'post_id' => $postId,
                'parent_comment_id' => $parentId,
                'content' => $comment['body'] ?? '',
                'author' => $comment['author'] ?? '[deleted]',
                'score' => $comment['score'] ?? 0,
                'created_utc' => $comment['created_utc'] ?? time()
            ];
            
            $comments[] = $commentData;
            
            // Process replies if they exist
            if (isset($comment['replies']['data']['children'])) {
                $this->extractCommentsRecursive(
                    $comment['replies']['data']['children'], 
                    $comments, 
                    $postId, 
                    $comment['id']
                );
            }
        }
    }
    
    /**
     * Get the 'after' parameter for pagination
     */
    public function getAfterParameter($response) {
        return $response['data']['after'] ?? null;
    }
    
    /**
     * Check if there are more pages
     */
    public function hasMorePages($response) {
        return !empty($response['data']['after']);
    }
}
