<?php
/**
 * Contact Information Extractor for Reddit Dubai Real Estate Scraper
 */

class ContactExtractor {
    
    /**
     * Extract all contact information from text
     */
    public function extractContacts($text, $author = null) {
        $contacts = [];
        
        // Extract phone numbers
        $phones = $this->extractPhoneNumbers($text);
        foreach ($phones as $phone) {
            $contacts[] = [
                'type' => 'phone',
                'value' => $this->cleanPhoneNumber($phone),
                'raw_value' => $phone,
                'author' => $author,
                'context' => $this->getContext($text, $phone)
            ];
        }
        
        // Extract emails
        $emails = $this->extractEmails($text);
        foreach ($emails as $email) {
            $contacts[] = [
                'type' => 'email',
                'value' => strtolower(trim($email)),
                'raw_value' => $email,
                'author' => $author,
                'context' => $this->getContext($text, $email)
            ];
        }
        
        // Extract WhatsApp numbers
        $whatsapp = $this->extractWhatsAppNumbers($text);
        foreach ($whatsapp as $wa) {
            $contacts[] = [
                'type' => 'whatsapp',
                'value' => $this->cleanPhoneNumber($wa),
                'raw_value' => $wa,
                'author' => $author,
                'context' => $this->getContext($text, $wa)
            ];
        }
        
        return $this->deduplicateContacts($contacts);
    }
    
    /**
     * Extract phone numbers using multiple patterns
     */
    private function extractPhoneNumbers($text) {
        $phones = [];
        
        foreach (PHONE_PATTERNS as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                $phones = array_merge($phones, $matches[0]);
            }
        }
        
        // Additional UAE-specific patterns
        $uaePatterns = [
            // Standard UAE mobile format
            '/(?:\+971|971|0)?[\s\-]?(?:50|55|56|52|54|58)[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}/',
            // UAE landline format
            '/(?:\+971|971|0)?[\s\-]?(?:2|3|4|6|7|9)[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}/',
            // International format with country code
            '/\+971[\s\-]?[0-9]{1,2}[\s\-]?[0-9]{3}[\s\-]?[0-9]{4}/',
            // Common formats people use
            '/(?:call|contact|phone|mobile|tel)[\s:]*(\+?[0-9\s\-\(\)]{8,15})/i',
            // Numbers in parentheses or with dashes
            '/\(?(?:\+971|971|0)?[\s\-]?(?:50|55|56|52|54|58)[\s\-]?\)?[0-9]{3}[\s\-]?[0-9]{4}/'
        ];
        
        foreach ($uaePatterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                $phones = array_merge($phones, $matches[0]);
            }
        }
        
        return array_unique($phones);
    }
    
    /**
     * Extract email addresses
     */
    private function extractEmails($text) {
        $emails = [];
        
        if (preg_match_all(EMAIL_PATTERN, $text, $matches)) {
            $emails = $matches[0];
        }
        
        // Additional email patterns for common variations
        $emailPatterns = [
            '/[a-zA-Z0-9._%+-]+\s*@\s*[a-zA-Z0-9.-]+\s*\.\s*[a-zA-Z]{2,}/',
            '/[a-zA-Z0-9._%+-]+\s*\[\s*at\s*\]\s*[a-zA-Z0-9.-]+\s*\[\s*dot\s*\]\s*[a-zA-Z]{2,}/i',
            '/[a-zA-Z0-9._%+-]+\s*\(\s*at\s*\)\s*[a-zA-Z0-9.-]+\s*\(\s*dot\s*\)\s*[a-zA-Z]{2,}/i'
        ];
        
        foreach ($emailPatterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                foreach ($matches[0] as $match) {
                    // Clean up obfuscated emails
                    $cleaned = str_replace(['[at]', '(at)', '[dot]', '(dot)'], ['@', '@', '.', '.'], $match);
                    $cleaned = preg_replace('/\s+/', '', $cleaned);
                    if (filter_var($cleaned, FILTER_VALIDATE_EMAIL)) {
                        $emails[] = $cleaned;
                    }
                }
            }
        }
        
        return array_unique($emails);
    }
    
    /**
     * Extract WhatsApp numbers
     */
    private function extractWhatsAppNumbers($text) {
        $whatsapp = [];
        
        $whatsappPatterns = [
            '/whatsapp[\s:]*(\+?[0-9\s\-\(\)]{8,15})/i',
            '/wa[\s:]*(\+?[0-9\s\-\(\)]{8,15})/i',
            '/what\'s\s*app[\s:]*(\+?[0-9\s\-\(\)]{8,15})/i',
            '/contact\s+me\s+on\s+(\+?[0-9\s\-\(\)]{8,15})/i',
            '/message\s+me\s+on\s+(\+?[0-9\s\-\(\)]{8,15})/i',
            '/text\s+me\s+on\s+(\+?[0-9\s\-\(\)]{8,15})/i'
        ];
        
        foreach ($whatsappPatterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                $whatsapp = array_merge($whatsapp, $matches[1]);
            }
        }
        
        return array_unique($whatsapp);
    }
    
    /**
     * Clean and standardize phone numbers
     */
    private function cleanPhoneNumber($phone) {
        // Remove all non-digit characters except +
        $cleaned = preg_replace('/[^\d+]/', '', $phone);
        
        // Handle UAE numbers
        if (preg_match('/^(?:\+971|971|0)(.+)/', $cleaned, $matches)) {
            $number = $matches[1];
            // Ensure it starts with valid UAE mobile prefix
            if (preg_match('/^(50|55|56|52|54|58)/', $number)) {
                return '+971' . $number;
            }
            // UAE landline
            if (preg_match('/^[234679]/', $number)) {
                return '+971' . $number;
            }
        }
        
        // If it already has country code
        if (strpos($cleaned, '+') === 0) {
            return $cleaned;
        }
        
        // If it's a UAE mobile without country code
        if (preg_match('/^(50|55|56|52|54|58)/', $cleaned)) {
            return '+971' . $cleaned;
        }
        
        return $cleaned;
    }
    
    /**
     * Get context around found contact information
     */
    private function getContext($text, $contact, $contextLength = 100) {
        $position = stripos($text, $contact);
        if ($position === false) {
            return '';
        }
        
        $start = max(0, $position - $contextLength);
        $length = strlen($contact) + (2 * $contextLength);
        
        $context = substr($text, $start, $length);
        
        // Clean up the context
        $context = trim($context);
        if ($start > 0) {
            $context = '...' . $context;
        }
        if ($start + $length < strlen($text)) {
            $context = $context . '...';
        }
        
        return $context;
    }
    
    /**
     * Remove duplicate contacts
     */
    private function deduplicateContacts($contacts) {
        $unique = [];
        $seen = [];
        
        foreach ($contacts as $contact) {
            $key = $contact['type'] . ':' . $contact['value'];
            if (!isset($seen[$key])) {
                $seen[$key] = true;
                $unique[] = $contact;
            }
        }
        
        return $unique;
    }
    
    /**
     * Validate phone number format
     */
    public function isValidPhoneNumber($phone) {
        $cleaned = $this->cleanPhoneNumber($phone);
        
        // Must be at least 8 digits and at most 15 digits (international standard)
        if (!preg_match('/^\+?[1-9]\d{7,14}$/', $cleaned)) {
            return false;
        }
        
        // UAE specific validation
        if (strpos($cleaned, '+971') === 0) {
            $number = substr($cleaned, 4);
            // Mobile numbers: 50, 55, 56, 52, 54, 58 followed by 7 digits
            if (preg_match('/^(50|55|56|52|54|58)\d{7}$/', $number)) {
                return true;
            }
            // Landline numbers: 2, 3, 4, 6, 7, 9 followed by 7 digits
            if (preg_match('/^[234679]\d{7}$/', $number)) {
                return true;
            }
        }
        
        return true; // Allow other international numbers
    }
    
    /**
     * Validate email address
     */
    public function isValidEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Check if contact information looks legitimate
     */
    public function isLegitimateContact($contact) {
        switch ($contact['type']) {
            case 'phone':
            case 'whatsapp':
                return $this->isValidPhoneNumber($contact['value']);
            case 'email':
                return $this->isValidEmail($contact['value']);
            default:
                return false;
        }
    }
    
    /**
     * Extract contact information with confidence scoring
     */
    public function extractContactsWithScore($text, $author = null) {
        $contacts = $this->extractContacts($text, $author);
        
        foreach ($contacts as &$contact) {
            $contact['confidence_score'] = $this->calculateConfidenceScore($contact, $text);
        }
        
        // Sort by confidence score
        usort($contacts, function($a, $b) {
            return $b['confidence_score'] - $a['confidence_score'];
        });
        
        return $contacts;
    }
    
    /**
     * Calculate confidence score for contact information
     */
    private function calculateConfidenceScore($contact, $text) {
        $score = 0;
        
        // Base score for valid format
        if ($this->isLegitimateContact($contact)) {
            $score += 50;
        }
        
        // Bonus for explicit contact keywords
        $contactKeywords = ['contact', 'call', 'phone', 'email', 'whatsapp', 'message', 'reach'];
        foreach ($contactKeywords as $keyword) {
            if (stripos($contact['context'], $keyword) !== false) {
                $score += 10;
                break;
            }
        }
        
        // Bonus for UAE numbers
        if (in_array($contact['type'], ['phone', 'whatsapp']) && strpos($contact['value'], '+971') === 0) {
            $score += 20;
        }
        
        // Penalty for common false positives
        $falsePosPatterns = ['/\d{4}-\d{4}/', '/\d{2}:\d{2}/', '/\d{1,2}\/\d{1,2}\/\d{4}/'];
        foreach ($falsePosPatterns as $pattern) {
            if (preg_match($pattern, $contact['raw_value'])) {
                $score -= 30;
            }
        }
        
        return max(0, min(100, $score));
    }
}
