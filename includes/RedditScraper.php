<?php
/**
 * Main Reddit Scraper for Dubai Real Estate Leads
 */

require_once 'Database.php';
require_once 'RedditAPISimple.php';
require_once 'ContactExtractor.php';
require_once 'IntentAnalyzer.php';

class RedditScraper {
    
    private $db;
    private $reddit;
    private $contactExtractor;
    private $intentAnalyzer;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->reddit = new RedditAPISimple();
        $this->contactExtractor = new ContactExtractor();
        $this->intentAnalyzer = new IntentAnalyzer();
    }
    
    /**
     * Scrape all target subreddits
     */
    public function scrapeAll() {
        $results = [
            'total_posts' => 0,
            'total_comments' => 0,
            'total_leads' => 0,
            'subreddits' => []
        ];
        
        foreach (TARGET_SUBREDDITS as $subreddit) {
            try {
                $subredditResults = $this->scrapeSubreddit($subreddit);
                $results['subreddits'][$subreddit] = $subredditResults;
                $results['total_posts'] += $subredditResults['posts'];
                $results['total_comments'] += $subredditResults['comments'];
                $results['total_leads'] += $subredditResults['leads'];
                
                // Rate limiting
                $this->reddit->respectRateLimit();
                
            } catch (Exception $e) {
                $this->logError($subreddit, 'scrape_subreddit', $e->getMessage());
                $results['subreddits'][$subreddit] = ['error' => $e->getMessage()];
            }
        }
        
        return $results;
    }
    
    /**
     * Scrape a specific subreddit
     */
    public function scrapeSubreddit($subreddit, $limit = null) {
        $startTime = microtime(true);
        $limit = $limit ?? MAX_POSTS_PER_SUBREDDIT;
        
        $results = [
            'posts' => 0,
            'comments' => 0,
            'leads' => 0,
            'errors' => []
        ];
        
        try {
            // Get subreddit ID
            $subredditId = $this->db->getSubredditId($subreddit);
            if (!$subredditId) {
                throw new Exception("Subreddit not found in database: $subreddit");
            }
            
            $after = null;
            $postsProcessed = 0;
            
            while ($postsProcessed < $limit) {
                $requestLimit = min(POSTS_PER_REQUEST, $limit - $postsProcessed);
                
                // Get posts from Reddit
                $response = $this->reddit->getSubredditPosts($subreddit, 'new', $requestLimit, $after);
                $posts = $this->reddit->extractPostsData($response);
                
                if (empty($posts)) {
                    break;
                }
                
                foreach ($posts as $postData) {
                    try {
                        $postResults = $this->processPost($postData, $subredditId);
                        $results['posts'] += $postResults['posts'];
                        $results['comments'] += $postResults['comments'];
                        $results['leads'] += $postResults['leads'];
                        
                        $postsProcessed++;
                        
                        // Rate limiting between posts
                        usleep(500000); // 0.5 second delay
                        
                    } catch (Exception $e) {
                        $results['errors'][] = "Error processing post {$postData['reddit_id']}: " . $e->getMessage();
                    }
                }
                
                // Check for more pages
                $after = $this->reddit->getAfterParameter($response);
                if (!$after || !$this->reddit->hasMorePages($response)) {
                    break;
                }
                
                // Rate limiting between pages
                $this->reddit->respectRateLimit();
            }
            
            // Update subreddit last scraped time
            $this->db->updateSubredditLastScraped($subreddit);
            
            $executionTime = microtime(true) - $startTime;
            
            // Log successful scraping
            $this->db->logScraping([
                'subreddit' => $subreddit,
                'action' => 'scrape_subreddit',
                'status' => 'success',
                'message' => "Scraped {$results['posts']} posts, {$results['comments']} comments, found {$results['leads']} leads",
                'posts_found' => $results['posts'],
                'comments_found' => $results['comments'],
                'leads_found' => $results['leads'],
                'execution_time' => $executionTime
            ]);
            
        } catch (Exception $e) {
            $this->logError($subreddit, 'scrape_subreddit', $e->getMessage());
            throw $e;
        }
        
        return $results;
    }
    
    /**
     * Process a single post and its comments
     */
    private function processPost($postData, $subredditId) {
        $results = ['posts' => 0, 'comments' => 0, 'leads' => 0];
        
        // Analyze post intent
        $intentAnalysis = $this->intentAnalyzer->analyzeIntent(
            $postData['content'], 
            $postData['title'], 
            true
        );
        
        // Extract contacts from post
        $postContacts = $this->contactExtractor->extractContactsWithScore(
            $postData['title'] . ' ' . $postData['content'],
            $postData['author']
        );
        
        // Prepare post data for database
        $dbPostData = [
            'reddit_id' => $postData['reddit_id'],
            'subreddit_id' => $subredditId,
            'title' => $postData['title'],
            'content' => $postData['content'],
            'author' => $postData['author'],
            'url' => $postData['url'],
            'score' => $postData['score'],
            'num_comments' => $postData['num_comments'],
            'created_utc' => $postData['created_utc'],
            'intent_score' => $intentAnalysis['total_score'],
            'has_contact_info' => !empty($postContacts)
        ];
        
        // Insert post into database
        if ($this->db->insertPost($dbPostData)) {
            $results['posts'] = 1;
            
            $postId = $this->db->getPostByRedditId($postData['reddit_id']);
            
            // Save contacts as leads
            foreach ($postContacts as $contact) {
                if ($contact['confidence_score'] >= 50) { // Only high-confidence contacts
                    $leadData = [
                        'source_type' => 'post',
                        'source_id' => $postId,
                        'contact_type' => $contact['type'],
                        'contact_value' => $contact['value'],
                        'author' => $contact['author'],
                        'intent_score' => $intentAnalysis['total_score'],
                        'context' => $contact['context']
                    ];
                    
                    if ($this->db->insertLead($leadData)) {
                        $results['leads']++;
                    }
                }
            }
            
            // Save keyword matches
            $keywords = $this->intentAnalyzer->extractKeywords($postData['content'], $postData['title']);
            foreach ($keywords as $keyword) {
                $this->db->insertKeywordMatch([
                    'source_type' => 'post',
                    'source_id' => $postId,
                    'keyword_type' => $keyword['type'],
                    'keyword' => $keyword['keyword'],
                    'context' => $keyword['context']
                ]);
            }
            
            // Process comments if the post has good intent score
            if ($intentAnalysis['total_score'] >= MIN_INTENT_SCORE || !empty($postContacts)) {
                $commentResults = $this->processPostComments($postData, $postId);
                $results['comments'] += $commentResults['comments'];
                $results['leads'] += $commentResults['leads'];
            }
        }
        
        return $results;
    }
    
    /**
     * Process comments for a post
     */
    private function processPostComments($postData, $postId) {
        $results = ['comments' => 0, 'leads' => 0];
        
        try {
            // Get comments from Reddit
            $response = $this->reddit->getPostComments($postData['subreddit'], $postData['reddit_id']);
            $comments = $this->reddit->extractCommentsData($response, $postId);
            
            foreach ($comments as $commentData) {
                // Analyze comment intent
                $intentAnalysis = $this->intentAnalyzer->analyzeIntent($commentData['content'], '', false);
                
                // Extract contacts from comment
                $commentContacts = $this->contactExtractor->extractContactsWithScore(
                    $commentData['content'],
                    $commentData['author']
                );
                
                // Prepare comment data for database
                $dbCommentData = [
                    'reddit_id' => $commentData['reddit_id'],
                    'post_id' => $postId,
                    'parent_comment_id' => $commentData['parent_comment_id'],
                    'content' => $commentData['content'],
                    'author' => $commentData['author'],
                    'score' => $commentData['score'],
                    'created_utc' => $commentData['created_utc'],
                    'intent_score' => $intentAnalysis['total_score'],
                    'has_contact_info' => !empty($commentContacts)
                ];
                
                // Insert comment into database
                if ($this->db->insertComment($dbCommentData)) {
                    $results['comments']++;
                    
                    // Save contacts as leads
                    foreach ($commentContacts as $contact) {
                        if ($contact['confidence_score'] >= 50) {
                            $leadData = [
                                'source_type' => 'comment',
                                'source_id' => $commentData['reddit_id'], // Use comment reddit_id
                                'contact_type' => $contact['type'],
                                'contact_value' => $contact['value'],
                                'author' => $contact['author'],
                                'intent_score' => $intentAnalysis['total_score'],
                                'context' => $contact['context']
                            ];
                            
                            if ($this->db->insertLead($leadData)) {
                                $results['leads']++;
                            }
                        }
                    }
                    
                    // Save keyword matches
                    $keywords = $this->intentAnalyzer->extractKeywords($commentData['content']);
                    foreach ($keywords as $keyword) {
                        $this->db->insertKeywordMatch([
                            'source_type' => 'comment',
                            'source_id' => $commentData['reddit_id'],
                            'keyword_type' => $keyword['type'],
                            'keyword' => $keyword['keyword'],
                            'context' => $keyword['context']
                        ]);
                    }
                }
            }
            
        } catch (Exception $e) {
            $this->logError($postData['subreddit'], 'process_comments', $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Search for specific keywords in subreddits
     */
    public function searchKeywords($subreddit, $keywords, $limit = 25) {
        $results = [];
        
        foreach ($keywords as $keyword) {
            try {
                $response = $this->reddit->searchSubreddit($subreddit, $keyword, 'new', $limit);
                $posts = $this->reddit->extractPostsData($response);
                
                foreach ($posts as $post) {
                    $results[] = $this->processPost($post, $this->db->getSubredditId($subreddit));
                }
                
                $this->reddit->respectRateLimit();
                
            } catch (Exception $e) {
                $this->logError($subreddit, 'search_keywords', $e->getMessage());
            }
        }
        
        return $results;
    }
    
    /**
     * Log errors
     */
    private function logError($subreddit, $action, $message) {
        $this->db->logScraping([
            'subreddit' => $subreddit,
            'action' => $action,
            'status' => 'error',
            'message' => $message,
            'execution_time' => 0
        ]);
    }
    
    /**
     * Get scraping statistics
     */
    public function getStats() {
        $pdo = $this->db->getPDO();
        
        $stats = [];
        
        // Total counts
        $stats['total_posts'] = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
        $stats['total_comments'] = $pdo->query("SELECT COUNT(*) FROM comments")->fetchColumn();
        $stats['total_leads'] = $pdo->query("SELECT COUNT(*) FROM leads")->fetchColumn();
        
        // Leads by status
        $stmt = $pdo->query("SELECT status, COUNT(*) as count FROM leads GROUP BY status");
        $stats['leads_by_status'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // Recent activity
        $stmt = $pdo->query("SELECT DATE(created_at) as date, COUNT(*) as count FROM leads WHERE created_at >= date('now', '-7 days') GROUP BY DATE(created_at) ORDER BY date DESC");
        $stats['recent_leads'] = $stmt->fetchAll();
        
        return $stats;
    }
}
