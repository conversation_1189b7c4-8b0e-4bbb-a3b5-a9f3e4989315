<?php
/**
 * Buying Intent Analyzer for Reddit Dubai Real Estate Scraper
 */

class IntentAnalyzer {
    
    private $buyingKeywords;
    private $locationKeywords;
    private $scoreWeights;
    
    public function __construct() {
        $this->buyingKeywords = BUYING_KEYWORDS;
        $this->locationKeywords = LOCATION_KEYWORDS;
        $this->scoreWeights = INTENT_SCORE_WEIGHTS;
    }
    
    /**
     * Analyze text for buying intent and return score with details
     */
    public function analyzeIntent($text, $title = '', $isPost = true) {
        $analysis = [
            'total_score' => 0,
            'buying_keywords' => [],
            'location_keywords' => [],
            'has_contact_info' => false,
            'urgency_indicators' => [],
            'budget_indicators' => [],
            'property_type' => [],
            'details' => [],
            'is_seller' => false
        ];

        $fullText = $title . ' ' . $text;
        $fullText = strtolower($fullText);

        // Check for contact information first - if someone shares contact info, they're a potential lead
        $contactExtractor = new ContactExtractor();
        $contacts = $contactExtractor->extractContacts($text . ' ' . $title);
        $analysis['has_contact_info'] = !empty($contacts);

        // If they have contact info, give them a good base score
        if ($analysis['has_contact_info']) {
            $analysis['total_score'] += 10; // Base score for having contact info
        }
        
        // Analyze buying keywords
        $buyingScore = $this->analyzeBuyingKeywords($fullText, $analysis);
        
        // Analyze location keywords
        $locationScore = $this->analyzeLocationKeywords($fullText, $analysis);
        
        // Analyze urgency indicators
        $urgencyScore = $this->analyzeUrgencyIndicators($fullText, $analysis);
        
        // Analyze budget indicators
        $budgetScore = $this->analyzeBudgetIndicators($fullText, $analysis);
        
        // Analyze property type preferences
        $propertyScore = $this->analyzePropertyType($fullText, $analysis);
        
        // Check for contact information
        $contactExtractor = new ContactExtractor();
        $contacts = $contactExtractor->extractContacts($text);
        $analysis['has_contact_info'] = !empty($contacts);
        $contactScore = $analysis['has_contact_info'] ? $this->scoreWeights['contact_info'] : 0;
        
        // Calculate weighted total score
        $analysis['total_score'] = 
            ($buyingScore * $this->scoreWeights['buying_keywords']) +
            ($locationScore * $this->scoreWeights['location_keywords']) +
            ($contactScore) +
            ($urgencyScore) +
            ($budgetScore) +
            ($propertyScore);
        
        // Bonus for post title vs comment
        if ($isPost && !empty($title)) {
            $titleScore = $this->analyzeTitleIntent($title);
            $analysis['total_score'] += ($titleScore * $this->scoreWeights['post_title']);
        }
        
        return $analysis;
    }
    
    /**
     * Analyze seller indicators (to exclude)
     */
    private function analyzeSellerIndicators($text, &$analysis) {
        $score = 0;
        $found = [];

        foreach (SELLER_KEYWORDS as $keyword) {
            if (strpos($text, strtolower($keyword)) !== false) {
                $found[] = $keyword;
                $score += 1;
            }
        }

        $analysis['seller_indicators'] = $found;
        return $score;
    }

    /**
     * Analyze buying keywords in text
     */
    private function analyzeBuyingKeywords($text, &$analysis) {
        $score = 0;
        $found = [];

        foreach ($this->buyingKeywords as $keyword) {
            if (strpos($text, strtolower($keyword)) !== false) {
                $found[] = $keyword;
                $score += $this->getKeywordWeight($keyword);
            }
        }

        $analysis['buying_keywords'] = $found;
        return min($score, 10); // Cap at 10 points
    }
    
    /**
     * Analyze location keywords in text
     */
    private function analyzeLocationKeywords($text, &$analysis) {
        $score = 0;
        $found = [];
        
        foreach ($this->locationKeywords as $location) {
            if (strpos($text, strtolower($location)) !== false) {
                $found[] = $location;
                $score += 1;
            }
        }
        
        $analysis['location_keywords'] = $found;
        return min($score, 5); // Cap at 5 points
    }
    
    /**
     * Analyze urgency indicators
     */
    private function analyzeUrgencyIndicators($text, &$analysis) {
        $urgencyKeywords = [
            'urgent' => 3,
            'asap' => 3,
            'immediately' => 3,
            'soon' => 2,
            'this month' => 2,
            'next month' => 1,
            'quick' => 2,
            'fast' => 2,
            'ready to buy' => 4,
            'cash buyer' => 4,
            'serious buyer' => 3
        ];
        
        $score = 0;
        $found = [];
        
        foreach ($urgencyKeywords as $keyword => $weight) {
            if (strpos($text, $keyword) !== false) {
                $found[] = $keyword;
                $score += $weight;
            }
        }
        
        $analysis['urgency_indicators'] = $found;
        return min($score, 8); // Cap at 8 points
    }
    
    /**
     * Analyze budget indicators
     */
    private function analyzeBudgetIndicators($text, &$analysis) {
        $budgetPatterns = [
            '/budget[\s:]*(?:is|of)?[\s]*(?:aed|dhs)?[\s]*([0-9,]+(?:\.[0-9]+)?)\s*(?:million|m|k|thousand)?/i',
            '/price\s+range[\s:]*(?:aed|dhs)?[\s]*([0-9,]+(?:\.[0-9]+)?)\s*(?:to|-)?\s*(?:aed|dhs)?[\s]*([0-9,]+(?:\.[0-9]+)?)?/i',
            '/looking\s+for[\s\w]*(?:under|below|around|approximately)[\s]*(?:aed|dhs)?[\s]*([0-9,]+(?:\.[0-9]+)?)/i',
            '/can\s+afford[\s]*(?:up\s+to)?[\s]*(?:aed|dhs)?[\s]*([0-9,]+(?:\.[0-9]+)?)/i',
            '/([0-9,]+(?:\.[0-9]+)?)\s*(?:million|m)\s*(?:aed|dhs|budget|price)/i'
        ];
        
        $score = 0;
        $found = [];
        
        foreach ($budgetPatterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                foreach ($matches[0] as $match) {
                    $found[] = trim($match);
                    $score += 3;
                }
            }
        }
        
        // Look for general budget mentions
        $budgetKeywords = ['budget', 'price range', 'afford', 'financing', 'mortgage', 'down payment'];
        foreach ($budgetKeywords as $keyword) {
            if (strpos($text, $keyword) !== false && !in_array($keyword, $found)) {
                $found[] = $keyword;
                $score += 1;
            }
        }
        
        $analysis['budget_indicators'] = $found;
        return min($score, 6); // Cap at 6 points
    }
    
    /**
     * Analyze property type preferences
     */
    private function analyzePropertyType($text, &$analysis) {
        $propertyTypes = [
            'apartment' => 2,
            'villa' => 2,
            'townhouse' => 2,
            'penthouse' => 2,
            'studio' => 1,
            '1 bedroom' => 1,
            '2 bedroom' => 1,
            '3 bedroom' => 1,
            '4 bedroom' => 1,
            '1br' => 1,
            '2br' => 1,
            '3br' => 1,
            '4br' => 1,
            'duplex' => 2,
            'loft' => 1,
            'flat' => 1
        ];
        
        $score = 0;
        $found = [];
        
        foreach ($propertyTypes as $type => $weight) {
            if (strpos($text, $type) !== false) {
                $found[] = $type;
                $score += $weight;
            }
        }
        
        $analysis['property_type'] = $found;
        return min($score, 4); // Cap at 4 points
    }
    
    /**
     * Analyze title for buying intent
     */
    private function analyzeTitleIntent($title) {
        $title = strtolower($title);
        $score = 0;
        
        // Strong title indicators
        $strongIndicators = [
            'looking to buy',
            'want to buy',
            'buying advice',
            'first time buyer',
            'property search',
            'apartment hunting',
            'villa hunting'
        ];
        
        foreach ($strongIndicators as $indicator) {
            if (strpos($title, $indicator) !== false) {
                $score += 5;
            }
        }
        
        // Question indicators
        if (strpos($title, '?') !== false) {
            $questionKeywords = ['buy', 'purchase', 'invest', 'property', 'apartment', 'villa'];
            foreach ($questionKeywords as $keyword) {
                if (strpos($title, $keyword) !== false) {
                    $score += 2;
                    break;
                }
            }
        }
        
        return min($score, 10);
    }
    
    /**
     * Get weight for specific buying keywords
     */
    private function getKeywordWeight($keyword) {
        $highValueKeywords = [
            'looking to buy' => 5,
            'want to buy' => 5,
            'need to buy' => 5,
            'planning to buy' => 4,
            'first time buyer' => 4,
            'searching for' => 3,
            'looking for apartment' => 4,
            'looking for villa' => 4,
            'looking for property' => 4,
            'need apartment' => 4,
            'need villa' => 4,
            'apartment hunt' => 4,
            'villa hunt' => 4,
            'house hunting' => 4,
            'relocating to dubai' => 3,
            'moving to dubai' => 3,
            'new to dubai' => 2,
            'budget is' => 3,
            'my budget' => 3,
            'can afford' => 3,
            'seeking apartment' => 3,
            'seeking villa' => 3
        ];

        return $highValueKeywords[$keyword] ?? 1;
    }
    
    /**
     * Check if content meets minimum intent threshold
     */
    public function meetsIntentThreshold($analysis) {
        return $analysis['total_score'] >= MIN_INTENT_SCORE;
    }
    
    /**
     * Get intent level description
     */
    public function getIntentLevel($score) {
        if ($score >= 20) return 'Very High';
        if ($score >= 15) return 'High';
        if ($score >= 10) return 'Medium';
        if ($score >= 5) return 'Low';
        return 'Very Low';
    }
    
    /**
     * Extract all keywords found in text
     */
    public function extractKeywords($text, $title = '') {
        $keywords = [];
        $fullText = strtolower($title . ' ' . $text);
        
        // Extract buying keywords
        foreach ($this->buyingKeywords as $keyword) {
            if (strpos($fullText, strtolower($keyword)) !== false) {
                $keywords[] = [
                    'type' => 'buying',
                    'keyword' => $keyword,
                    'context' => $this->getKeywordContext($fullText, $keyword)
                ];
            }
        }
        
        // Extract location keywords
        foreach ($this->locationKeywords as $location) {
            if (strpos($fullText, strtolower($location)) !== false) {
                $keywords[] = [
                    'type' => 'location',
                    'keyword' => $location,
                    'context' => $this->getKeywordContext($fullText, $location)
                ];
            }
        }
        
        return $keywords;
    }
    
    /**
     * Get context around a keyword
     */
    private function getKeywordContext($text, $keyword, $contextLength = 50) {
        $position = stripos($text, $keyword);
        if ($position === false) {
            return '';
        }
        
        $start = max(0, $position - $contextLength);
        $length = strlen($keyword) + (2 * $contextLength);
        
        $context = substr($text, $start, $length);
        return trim($context);
    }
    
    /**
     * Analyze multiple texts and return aggregated score
     */
    public function analyzeMultipleTexts($texts, $weights = null) {
        $totalScore = 0;
        $allKeywords = [];
        $hasContact = false;
        
        $defaultWeights = $weights ?? [1.0]; // Equal weight if not specified
        
        foreach ($texts as $index => $textData) {
            $weight = $defaultWeights[$index] ?? 1.0;
            $analysis = $this->analyzeIntent(
                $textData['text'] ?? '', 
                $textData['title'] ?? '', 
                $textData['is_post'] ?? false
            );
            
            $totalScore += ($analysis['total_score'] * $weight);
            $allKeywords = array_merge($allKeywords, $analysis['buying_keywords'], $analysis['location_keywords']);
            $hasContact = $hasContact || $analysis['has_contact_info'];
        }
        
        return [
            'total_score' => $totalScore,
            'keywords' => array_unique($allKeywords),
            'has_contact_info' => $hasContact,
            'intent_level' => $this->getIntentLevel($totalScore)
        ];
    }
}
