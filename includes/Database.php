<?php
/**
 * Database class for Reddit Dubai Real Estate Scraper
 */

class Database {
    private $pdo;
    private static $instance = null;
    
    private function __construct() {
        $this->connect();
        $this->createTables();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        try {
            $this->pdo = new PDO('sqlite:' . DB_PATH);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            
            // Enable foreign keys
            $this->pdo->exec('PRAGMA foreign_keys = ON');
        } catch (PDOException $e) {
            die('Database connection failed: ' . $e->getMessage());
        }
    }
    
    private function createTables() {
        $sql = "
        -- Subreddits table
        CREATE TABLE IF NOT EXISTS subreddits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) UNIQUE NOT NULL,
            last_scraped DATETIME,
            total_posts_scraped INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Posts table
        CREATE TABLE IF NOT EXISTS posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reddit_id VARCHAR(20) UNIQUE NOT NULL,
            subreddit_id INTEGER,
            title TEXT NOT NULL,
            content TEXT,
            author VARCHAR(50),
            url TEXT,
            score INTEGER DEFAULT 0,
            num_comments INTEGER DEFAULT 0,
            created_utc INTEGER,
            scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            intent_score INTEGER DEFAULT 0,
            has_contact_info BOOLEAN DEFAULT 0,
            FOREIGN KEY (subreddit_id) REFERENCES subreddits(id)
        );
        
        -- Comments table
        CREATE TABLE IF NOT EXISTS comments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reddit_id VARCHAR(20) UNIQUE NOT NULL,
            post_id INTEGER,
            parent_comment_id INTEGER,
            content TEXT NOT NULL,
            author VARCHAR(50),
            score INTEGER DEFAULT 0,
            created_utc INTEGER,
            scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            intent_score INTEGER DEFAULT 0,
            has_contact_info BOOLEAN DEFAULT 0,
            FOREIGN KEY (post_id) REFERENCES posts(id),
            FOREIGN KEY (parent_comment_id) REFERENCES comments(id)
        );
        
        -- Leads table (extracted contact information)
        CREATE TABLE IF NOT EXISTS leads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_type ENUM('post', 'comment') NOT NULL,
            source_id INTEGER NOT NULL,
            contact_type ENUM('phone', 'email', 'whatsapp') NOT NULL,
            contact_value VARCHAR(255) NOT NULL,
            author VARCHAR(50),
            intent_score INTEGER DEFAULT 0,
            context TEXT,
            status ENUM('new', 'contacted', 'qualified', 'not_interested', 'invalid') DEFAULT 'new',
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Keywords found table (for analysis)
        CREATE TABLE IF NOT EXISTS keyword_matches (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            source_type ENUM('post', 'comment') NOT NULL,
            source_id INTEGER NOT NULL,
            keyword_type ENUM('buying', 'location') NOT NULL,
            keyword VARCHAR(100) NOT NULL,
            context TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Scraping logs table
        CREATE TABLE IF NOT EXISTS scraping_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            subreddit VARCHAR(50),
            action VARCHAR(50),
            status ENUM('success', 'error', 'warning') NOT NULL,
            message TEXT,
            posts_found INTEGER DEFAULT 0,
            comments_found INTEGER DEFAULT 0,
            leads_found INTEGER DEFAULT 0,
            execution_time FLOAT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Settings table
        CREATE TABLE IF NOT EXISTS settings (
            key VARCHAR(100) PRIMARY KEY,
            value TEXT,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        ";
        
        try {
            $this->pdo->exec($sql);
            $this->insertDefaultSubreddits();
        } catch (PDOException $e) {
            die('Failed to create tables: ' . $e->getMessage());
        }
    }
    
    private function insertDefaultSubreddits() {
        $stmt = $this->pdo->prepare("INSERT OR IGNORE INTO subreddits (name) VALUES (?)");
        foreach (TARGET_SUBREDDITS as $subreddit) {
            $stmt->execute([$subreddit]);
        }
    }
    
    public function getPDO() {
        return $this->pdo;
    }
    
    public function insertPost($data) {
        $sql = "INSERT OR IGNORE INTO posts 
                (reddit_id, subreddit_id, title, content, author, url, score, num_comments, created_utc, intent_score, has_contact_info) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            $data['reddit_id'],
            $data['subreddit_id'],
            $data['title'],
            $data['content'],
            $data['author'],
            $data['url'],
            $data['score'],
            $data['num_comments'],
            $data['created_utc'],
            $data['intent_score'],
            $data['has_contact_info']
        ]);
    }
    
    public function insertComment($data) {
        $sql = "INSERT OR IGNORE INTO comments 
                (reddit_id, post_id, parent_comment_id, content, author, score, created_utc, intent_score, has_contact_info) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            $data['reddit_id'],
            $data['post_id'],
            $data['parent_comment_id'],
            $data['content'],
            $data['author'],
            $data['score'],
            $data['created_utc'],
            $data['intent_score'],
            $data['has_contact_info']
        ]);
    }
    
    public function insertLead($data) {
        $sql = "INSERT INTO leads 
                (source_type, source_id, contact_type, contact_value, author, intent_score, context) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            $data['source_type'],
            $data['source_id'],
            $data['contact_type'],
            $data['contact_value'],
            $data['author'],
            $data['intent_score'],
            $data['context']
        ]);
    }
    
    public function insertKeywordMatch($data) {
        $sql = "INSERT INTO keyword_matches 
                (source_type, source_id, keyword_type, keyword, context) 
                VALUES (?, ?, ?, ?, ?)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            $data['source_type'],
            $data['source_id'],
            $data['keyword_type'],
            $data['keyword'],
            $data['context']
        ]);
    }
    
    public function logScraping($data) {
        $sql = "INSERT INTO scraping_logs 
                (subreddit, action, status, message, posts_found, comments_found, leads_found, execution_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            $data['subreddit'],
            $data['action'],
            $data['status'],
            $data['message'],
            $data['posts_found'] ?? 0,
            $data['comments_found'] ?? 0,
            $data['leads_found'] ?? 0,
            $data['execution_time'] ?? 0
        ]);
    }
    
    public function getSubredditId($name) {
        $stmt = $this->pdo->prepare("SELECT id FROM subreddits WHERE name = ?");
        $stmt->execute([$name]);
        $result = $stmt->fetch();
        return $result ? $result['id'] : null;
    }
    
    public function updateSubredditLastScraped($name) {
        $sql = "UPDATE subreddits SET last_scraped = CURRENT_TIMESTAMP, total_posts_scraped = total_posts_scraped + 1 WHERE name = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$name]);
    }
    
    public function getPostByRedditId($reddit_id) {
        $stmt = $this->pdo->prepare("SELECT id FROM posts WHERE reddit_id = ?");
        $stmt->execute([$reddit_id]);
        $result = $stmt->fetch();
        return $result ? $result['id'] : null;
    }
}
