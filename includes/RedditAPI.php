<?php
/**
 * Reddit API Client for Dubai Real Estate Scraper
 */

class RedditAPI {
    private $accessToken;
    private $tokenExpiry;
    private $userAgent;
    private $clientId;
    private $clientSecret;
    private $baseUrl = 'https://oauth.reddit.com';
    private $authUrl = 'https://www.reddit.com/api/v1/access_token';
    
    public function __construct() {
        $this->clientId = REDDIT_CLIENT_ID;
        $this->clientSecret = REDDIT_CLIENT_SECRET;
        $this->userAgent = REDDIT_USER_AGENT;
        
        if (empty($this->clientId) || empty($this->clientSecret)) {
            throw new Exception('Reddit API credentials not configured. Please update config.php');
        }
    }
    
    /**
     * Get OAuth access token
     */
    private function getAccessToken() {
        if ($this->accessToken && time() < $this->tokenExpiry) {
            return $this->accessToken;
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->authUrl,
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'User-Agent: ' . $this->userAgent,
                'Authorization: Basic ' . base64_encode($this->clientId . ':' . $this->clientSecret)
            ],
            CURLOPT_POSTFIELDS => 'grant_type=client_credentials',
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('Failed to get Reddit access token. HTTP Code: ' . $httpCode);
        }
        
        $data = json_decode($response, true);
        if (!$data || !isset($data['access_token'])) {
            throw new Exception('Invalid response from Reddit OAuth');
        }
        
        $this->accessToken = $data['access_token'];
        $this->tokenExpiry = time() + $data['expires_in'] - 60; // 60 second buffer
        
        return $this->accessToken;
    }
    
    /**
     * Make authenticated API request
     */
    private function makeRequest($endpoint, $params = []) {
        $token = $this->getAccessToken();
        $url = $this->baseUrl . $endpoint;
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'User-Agent: ' . $this->userAgent,
                'Authorization: Bearer ' . $token
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('Reddit API request failed. HTTP Code: ' . $httpCode . ' Response: ' . $response);
        }
        
        $data = json_decode($response, true);
        if (!$data) {
            throw new Exception('Invalid JSON response from Reddit API');
        }
        
        return $data;
    }
    
    /**
     * Get posts from a subreddit
     */
    public function getSubredditPosts($subreddit, $sort = 'new', $limit = 25, $after = null) {
        $params = [
            'limit' => min($limit, 100),
            'sort' => $sort,
            'raw_json' => 1
        ];
        
        if ($after) {
            $params['after'] = $after;
        }
        
        $endpoint = "/r/{$subreddit}/{$sort}";
        return $this->makeRequest($endpoint, $params);
    }
    
    /**
     * Get comments for a specific post
     */
    public function getPostComments($subreddit, $postId, $limit = 100) {
        $params = [
            'limit' => min($limit, 500),
            'depth' => MAX_COMMENT_DEPTH,
            'raw_json' => 1
        ];
        
        $endpoint = "/r/{$subreddit}/comments/{$postId}";
        return $this->makeRequest($endpoint, $params);
    }
    
    /**
     * Search posts in a subreddit
     */
    public function searchSubreddit($subreddit, $query, $sort = 'new', $limit = 25) {
        $params = [
            'q' => $query,
            'restrict_sr' => 'true',
            'sort' => $sort,
            'limit' => min($limit, 100),
            'raw_json' => 1
        ];
        
        $endpoint = "/r/{$subreddit}/search";
        return $this->makeRequest($endpoint, $params);
    }
    
    /**
     * Get user information
     */
    public function getUserInfo($username) {
        $endpoint = "/user/{$username}/about";
        return $this->makeRequest($endpoint);
    }
    
    /**
     * Rate limiting helper
     */
    public function respectRateLimit() {
        // Reddit allows 60 requests per minute for OAuth
        // Sleep for 1 second between requests to be safe
        sleep(1);
    }
    
    /**
     * Extract posts data from Reddit API response
     */
    public function extractPostsData($response) {
        $posts = [];
        
        if (!isset($response['data']['children'])) {
            return $posts;
        }
        
        foreach ($response['data']['children'] as $child) {
            if ($child['kind'] !== 't3') continue; // t3 = Link/Post
            
            $post = $child['data'];
            $posts[] = [
                'reddit_id' => $post['id'],
                'title' => $post['title'] ?? '',
                'content' => $post['selftext'] ?? '',
                'author' => $post['author'] ?? '[deleted]',
                'url' => $post['url'] ?? '',
                'score' => $post['score'] ?? 0,
                'num_comments' => $post['num_comments'] ?? 0,
                'created_utc' => $post['created_utc'] ?? time(),
                'subreddit' => $post['subreddit'] ?? '',
                'permalink' => $post['permalink'] ?? '',
                'is_self' => $post['is_self'] ?? false
            ];
        }
        
        return $posts;
    }
    
    /**
     * Extract comments data from Reddit API response
     */
    public function extractCommentsData($response, $postId = null) {
        $comments = [];
        
        if (!is_array($response) || count($response) < 2) {
            return $comments;
        }
        
        // Comments are in the second element of the response array
        $commentsData = $response[1];
        
        if (!isset($commentsData['data']['children'])) {
            return $comments;
        }
        
        $this->extractCommentsRecursive($commentsData['data']['children'], $comments, $postId);
        
        return $comments;
    }
    
    /**
     * Recursively extract comments and replies
     */
    private function extractCommentsRecursive($children, &$comments, $postId = null, $parentId = null) {
        foreach ($children as $child) {
            if ($child['kind'] !== 't1') continue; // t1 = Comment
            
            $comment = $child['data'];
            
            // Skip deleted comments
            if ($comment['author'] === '[deleted]' || empty($comment['body'])) {
                continue;
            }
            
            $commentData = [
                'reddit_id' => $comment['id'],
                'post_id' => $postId,
                'parent_comment_id' => $parentId,
                'content' => $comment['body'] ?? '',
                'author' => $comment['author'] ?? '[deleted]',
                'score' => $comment['score'] ?? 0,
                'created_utc' => $comment['created_utc'] ?? time()
            ];
            
            $comments[] = $commentData;
            
            // Process replies if they exist
            if (isset($comment['replies']['data']['children'])) {
                $this->extractCommentsRecursive(
                    $comment['replies']['data']['children'], 
                    $comments, 
                    $postId, 
                    $comment['id']
                );
            }
        }
    }
    
    /**
     * Get the 'after' parameter for pagination
     */
    public function getAfterParameter($response) {
        return $response['data']['after'] ?? null;
    }
    
    /**
     * Check if there are more pages
     */
    public function hasMorePages($response) {
        return !empty($response['data']['after']);
    }
}
