<?php
/**
 * Test Reddit API Connection
 */

require_once 'config/config.php';
require_once 'includes/RedditAPI.php';

try {
    echo "Testing Reddit API connection...\n";
    
    $reddit = new RedditAPI();
    
    // Test by getting a few posts from r/test
    $response = $reddit->getSubredditPosts('test', 'hot', 5);
    
    if ($response && isset($response['data']['children'])) {
        echo "✅ Reddit API connection successful!\n";
        echo "Found " . count($response['data']['children']) . " posts from r/test\n";
        
        // Test extracting posts data
        $posts = $reddit->extractPostsData($response);
        echo "✅ Post extraction working - extracted " . count($posts) . " posts\n";
        
        if (!empty($posts)) {
            echo "Sample post: " . substr($posts[0]['title'], 0, 50) . "...\n";
        }
        
    } else {
        echo "❌ Reddit API connection failed - invalid response\n";
        exit(1);
    }
    
} catch (Exception $e) {
    echo "❌ Reddit API connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n🎉 All systems ready! You can now start scraping.\n";
?>
